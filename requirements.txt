alembic==1.16.4
annotated-types==0.7.0
anyio==4.9.0
appnope==0.1.4
APScheduler==3.11.0
asttokens==3.0.0
bs4==0.0.2
certifi==2025.7.14
charset-normalizer==3.4.2
click==8.2.1
comm==0.2.3
debugpy==1.8.15
decorator==5.2.1
deprecation==2.1.0
distro==1.9.0
executing==2.2.0
fastapi==0.116.1
h11==0.16.0
httpcore==1.0.9
httpx==0.28.1
idna==3.10
ipython==9.4.0
jedi==0.19.2
jiter==0.10.0
jsonpatch==1.33
jsonpointer==3.0.0
langchain==0.3.26
langchain-core==0.3.69
langchain-openai==0.3.28
langchain-text-splitters==0.3.8
langgraph==0.2.76
langgraph-checkpoint==2.1.0
langgraph-sdk==0.1.73
lark-oapi==1.4.19
loguru==0.7.3
Mako==1.3.10
MarkupSafe==3.0.2
nest-asyncio==1.6.0
numpy==1.26.4
openai==1.97.0
opencv-python==*********
orjson==3.11.0
ormsgpack==1.10.0
packaging==25.0
parso==0.8.4
pexpect==4.9.0
pillow==10.4.0
platformdirs==4.3.8
prompt_toolkit==3.0.51
ptyprocess==0.7.0
pure_eval==0.2.3
pydantic==2.11.7
pydantic-settings==2.10.1
pydantic_core==2.33.2
Pygments==2.19.2
PyMySQL==1.1.1
python-dotenv==1.1.1
python-multipart==0.0.20
pytz==2025.2
pyzmq==27.0.0
regex==2024.11.6
requests==2.32.4
requests-toolbelt==1.0.0
retry2==0.9.5
simplejson==3.20.1
six==1.17.0
sniffio==1.3.1
SQLAlchemy==2.0.41
stack-data==0.6.3
starlette==0.47.2
tenacity==9.1.2
tiktoken==0.9.0
tomli==2.2.1
tomlkit==0.13.3
tornado==6.5.1
tqdm==4.67.1
traitlets==5.14.3
tushare==1.4.21
typing-inspection==0.4.1
typing_extensions==4.14.1
tzdata==2025.2
tzlocal==5.3.1
urllib3==2.5.0
uvicorn==0.35.0
wcwidth==0.2.13
websockets==15.0.1
zstandard==0.23.0
