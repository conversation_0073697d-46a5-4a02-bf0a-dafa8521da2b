#!/usr/bin/env python3
"""
测试图像标记功能
"""

import cv2
import numpy as np
import os
import sys

# 添加父目录到路径以便导入 screen_shot 模块
sys.path.append(os.path.dirname(os.path.abspath(__file__)))
from screen_shot import mark_touch_on_image

def create_test_image():
    """创建一个测试图像"""
    # 创建一个 800x600 的白色图像
    img = np.ones((600, 800, 3), dtype=np.uint8) * 255
    
    # 添加一些网格线作为参考
    for i in range(0, 800, 100):
        cv2.line(img, (i, 0), (i, 600), (200, 200, 200), 1)
    for i in range(0, 600, 100):
        cv2.line(img, (0, i), (800, i), (200, 200, 200), 1)
    
    # 添加坐标标签
    for x in range(0, 800, 100):
        for y in range(0, 600, 100):
            cv2.putText(img, f"({x},{y})", (x+5, y+15), 
                       cv2.FONT_HERSHEY_SIMPLEX, 0.3, (100, 100, 100), 1)
    
    return img

def test_click_marking():
    """测试点击标记"""
    print("测试点击标记...")
    img = create_test_image()
    
    # 测试点击在 (400, 300) 位置
    marked_img = mark_touch_on_image(img, "click", None, None, 400, 300)
    
    output_path = "/Users/<USER>/Desktop/TT/code/click-pilot/tests/liangyedong/test_click_marking.png"
    cv2.imwrite(output_path, marked_img)
    print(f"点击标记测试图像保存到: {output_path}")

def test_swipe_marking():
    """测试滑动标记"""
    print("测试滑动标记...")
    img = create_test_image()
    
    # 测试从 (200, 150) 滑动到 (600, 450)
    marked_img = mark_touch_on_image(img, "swipe/drag", 200, 150, 600, 450)
    
    output_path = "/Users/<USER>/Desktop/TT/code/click-pilot/tests/liangyedong/test_swipe_marking.png"
    cv2.imwrite(output_path, marked_img)
    print(f"滑动标记测试图像保存到: {output_path}")

def test_real_screenshot_marking():
    """测试真实截图的标记"""
    print("测试真实截图标记...")
    
    # 获取最新的截图文件
    screenshot_dir = "/Users/<USER>/Desktop/TT/code/click-pilot/tests/liangyedong/screenshots"
    screenshots = [f for f in os.listdir(screenshot_dir) if f.endswith('.png')]
    if not screenshots:
        print("没有找到截图文件")
        return
    
    latest_screenshot = sorted(screenshots)[-1]
    screenshot_path = os.path.join(screenshot_dir, latest_screenshot)
    
    print(f"使用截图: {latest_screenshot}")
    
    # 读取截图
    img = cv2.imread(screenshot_path)
    if img is None:
        print("无法读取截图文件")
        return
    
    # 在截图上添加测试标记
    # 测试点击标记
    click_marked = mark_touch_on_image(img.copy(), "click", None, None, 400, 300)
    click_output = os.path.join(screenshot_dir, "test_real_click_marked.png")
    cv2.imwrite(click_output, click_marked)
    print(f"真实截图点击标记保存到: {click_output}")
    
    # 测试滑动标记
    swipe_marked = mark_touch_on_image(img.copy(), "swipe/drag", 100, 200, 500, 800)
    swipe_output = os.path.join(screenshot_dir, "test_real_swipe_marked.png")
    cv2.imwrite(swipe_output, swipe_marked)
    print(f"真实截图滑动标记保存到: {swipe_output}")

if __name__ == "__main__":
    print("开始测试图像标记功能...")
    
    test_click_marking()
    test_swipe_marking()
    test_real_screenshot_marking()
    
    print("测试完成！请检查生成的图像文件。")
