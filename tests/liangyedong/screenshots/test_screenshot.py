#!/usr/bin/env python3
"""
ADB 触摸和截图工具模块

依赖: 安装 adb，并在 PATH 中；开启 USB 调试
用法: python adb_touch_snap.py

该模块提供了通过 ADB 进行 Android 设备截图和触摸操作的功能。
"""
# 依赖: 安装 adb，并在 PATH 中；开启 USB 调试
# 用法: python adb_touch_snap.py
import subprocess
import re
import time
from datetime import datetime

# 可选：限定具体的触摸 event 设备，先用: adb shell getevent -lp | grep -i touchscreen -n
TOUCH_EVENT_DEV = None  # 例如 "/dev/input/event2"，不填则不过滤

DOWN_PATTERNS = [
    r'BTN_TOUCH\s+DOWN',           # 部分设备有这种语义化输出
    r'BTN_TOUCH\s+00000001',       # 原始十六进制数值=1
    r'ABS_MT_TRACKING_ID\s+(?!ffffffff)']  # 新的触点 id（不是 -1），视为 down


def screencap_now(tag_prefix="tap"):
    """截取当前屏幕截图并保存为PNG文件。

    Args:
        tag_prefix (str): 文件名前缀，默认为"tap"

    Returns:
        str: 保存的截图文件名
    """
    png = subprocess.check_output(["adb", "exec-out", "screencap", "-p"])
    ts = datetime.now().strftime("%Y%m%d_%H%M%S_%f")
    fname = f"{tag_prefix}_{ts}.png"
    with open(fname, "wb") as f:
        f.write(png)
    print("Saved:", fname)
    return fname


def main():
    """监听触摸事件并截图的主函数。
    
    启动adb监听触摸DOWN事件，当检测到触摸时自动截图。
    按Ctrl-C退出程序。
    """
    print("Listening touch DOWN, press Ctrl-C to exit ...")
    # -lt: 带时间戳；有的设备不支持 -t/-l 组合，可退化为 -l 或原始
    p = subprocess.Popen(
        ["adb", "shell", "getevent -lt"], stdout=subprocess.PIPE,
        stderr=subprocess.PIPE, text=True, bufsize=1, universal_newlines=True
    )
    if p.stdout:
        for line in p.stdout:
            s = line.strip()
            if not s:
                continue
            if TOUCH_EVENT_DEV and (TOUCH_EVENT_DEV not in s):
                continue

            # 只要匹配到"按下"语义，就立刻截屏
            if any(re.search(pat, s) for pat in DOWN_PATTERNS):
                # 立刻截一张，最大概率保留"跳转前"画面
                screencap_now("pre")

                # 可选：再延迟抓 1~2 张，帮助你对比跳转前后
                time.sleep(0.12)  # 120ms 后
                screencap_now("post1")
                time.sleep(0.12)
                screencap_now("post2")


if __name__ == "__main__":
    main()
