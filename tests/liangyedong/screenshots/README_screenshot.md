# Android操作截屏工具使用说明

## 功能对比

### 原版脚本 (`test_screenshot.py`)
- ✅ 检测触摸按下事件
- ✅ 自动截屏
- ❌ 只能检测点击，无法区分操作类型
- ❌ 无法捕获滑动轨迹
- ❌ 无法识别输入操作

### 增强版脚本 (`enhanced_screenshot.py`)
- ✅ 检测多种操作类型：点击、长按、滑动、拖拽
- ✅ 捕获操作轨迹和坐标
- ✅ 识别输入法状态
- ✅ 记录操作时长
- ✅ 保存详细元数据
- ✅ 生成操作日志

## 使用方法

### 1. 环境准备
```bash
# 确保ADB已安装并在PATH中
adb version

# 连接Android设备并开启USB调试
adb devices
```

### 2. 运行增强版工具
```bash
cd tests/liangyedong
python enhanced_screenshot.py
```

### 3. 操作设备
在设备上进行各种操作：
- 点击按钮
- 滑动屏幕
- 长按元素
- 输入文字

工具会自动：
- 在操作前后截屏
- 记录操作类型和轨迹
- 保存元数据信息

## 输出文件

### 截屏文件
- `before_operation_*.png` - 操作前截屏
- `after_operation_*.png` - 操作后截屏
- `after_input_*.png` - 输入后截屏（如有输入）

### 元数据文件
- `*_meta.json` - 每张截屏的详细信息
- `session_*.json` - 完整会话日志

### 元数据内容示例
```json
{
  "event_type": "touch_end",
  "operation_type": "swipe",
  "duration": 0.45,
  "positions": [[100, 200], [150, 250], [200, 300]],
  "timestamp": "2024-01-01T12:00:00",
  "activity": "com.example.app/.MainActivity",
  "input_method": {
    "input_active": false,
    "current_ime": null
  }
}
```

## 操作类型识别

- **tap**: 快速点击（<0.2秒，移动距离小）
- **long_press**: 长按（>1秒，位置基本不变）
- **swipe**: 滑动（移动距离>100像素）
- **drag**: 拖拽（移动距离较小但有明显轨迹）
- **touch**: 一般触摸操作

## 优势

1. **完整记录**：捕获操作前后的完整状态
2. **类型识别**：自动识别不同操作类型
3. **轨迹追踪**：记录滑动和拖拽的完整路径
4. **输入检测**：特别处理文字输入场景
5. **结构化数据**：便于后续分析和自动化测试