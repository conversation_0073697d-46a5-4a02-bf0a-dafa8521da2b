#!/usr/bin/env python3
# 增强版Android操作截屏工具
# 依赖: 安装 adb，并在 PATH 中；开启 USB 调试
import subprocess, re, time, json, os
from datetime import datetime
from typing import Dict, List, Optional

class AndroidOperationCapture:
    def __init__(self, output_dir: str = "screenshots"):
        self.output_dir = output_dir
        self.touch_event_dev = None
        self.current_session = None
        self.operation_log = []
        
        # 确保输出目录存在
        os.makedirs(output_dir, exist_ok=True)
        
        # 不同操作类型的检测模式
        self.patterns = {
            'touch_down': [
                r'BTN_TOUCH\s+DOWN',
                r'BTN_TOUCH\s+00000001',
                r'ABS_MT_TRACKING_ID\s+(?!ffffffff)'
            ],
            'touch_up': [
                r'BTN_TOUCH\s+UP',
                r'BTN_TOUCH\s+00000000'
            ],
            'move': [
                r'ABS_MT_POSITION_X',
                r'ABS_MT_POSITION_Y'
            ]
        }
        
        # 操作状态跟踪
        self.touch_start_time = None
        self.touch_positions = []
        self.is_touching = False

    def screencap_now(self, tag_prefix: str = "capture", metadata: Dict = None) -> str:
        """截屏并保存元数据"""
        try:
            png = subprocess.check_output(["adb", "exec-out", "screencap", "-p"])
            ts = datetime.now().strftime("%Y%m%d_%H%M%S_%f")
            fname = f"{tag_prefix}_{ts}.png"
            filepath = os.path.join(self.output_dir, fname)
            
            with open(filepath, "wb") as f:
                f.write(png)
            
            # 保存元数据
            if metadata:
                meta_file = filepath.replace('.png', '_meta.json')
                with open(meta_file, 'w', encoding='utf-8') as f:
                    json.dump(metadata, f, ensure_ascii=False, indent=2)
            
            print(f"📸 Saved: {filepath}")
            return filepath
        except Exception as e:
            print(f"❌ Screenshot failed: {e}")
            return None

    def get_current_activity(self) -> str:
        """获取当前活动的应用和Activity"""
        try:
            result = subprocess.check_output([
                "adb", "shell", "dumpsys", "window", "windows", "|", 
                "grep", "-E", "mCurrentFocus|mFocusedApp"
            ], text=True)
            return result.strip()
        except:
            return "Unknown"

    def get_input_method_info(self) -> Dict:
        """获取输入法状态信息"""
        try:
            result = subprocess.check_output([
                "adb", "shell", "dumpsys", "input_method"
            ], text=True)
            
            # 简单解析输入法状态
            is_active = "mInputShown=true" in result
            current_ime = re.search(r'mCurMethodId=([^\s]+)', result)
            
            return {
                "input_active": is_active,
                "current_ime": current_ime.group(1) if current_ime else None
            }
        except:
            return {"input_active": False, "current_ime": None}

    def analyze_operation_type(self, positions: List, duration: float) -> str:
        """分析操作类型"""
        if not positions:
            return "unknown"
        
        if len(positions) <= 2 and duration < 0.2:
            return "tap"  # 快速点击
        elif duration > 1.0 and len(positions) <= 3:
            return "long_press"  # 长按
        elif len(positions) > 5:
            # 计算移动距离
            start_x, start_y = positions[0]
            end_x, end_y = positions[-1]
            distance = ((end_x - start_x) ** 2 + (end_y - start_y) ** 2) ** 0.5
            
            if distance > 100:  # 移动距离阈值
                return "swipe"  # 滑动
            else:
                return "drag"   # 拖拽
        else:
            return "touch"  # 一般触摸

    def extract_coordinates(self, line: str) -> Optional[tuple]:
        """从事件行中提取坐标"""
        x_match = re.search(r'ABS_MT_POSITION_X\s+([0-9a-f]+)', line)
        y_match = re.search(r'ABS_MT_POSITION_Y\s+([0-9a-f]+)', line)
        
        if x_match and y_match:
            x = int(x_match.group(1), 16)
            y = int(y_match.group(1), 16)
            return (x, y)
        return None

    def start_monitoring(self):
        """开始监听操作"""
        print("🎯 开始监听Android设备操作...")
        print("📱 支持检测: 点击、长按、滑动、拖拽、输入")
        print("⏹️  按 Ctrl-C 停止监听")
        
        try:
            p = subprocess.Popen(
                ["adb", "shell", "getevent", "-lt"], 
                stdout=subprocess.PIPE,
                stderr=subprocess.PIPE, 
                text=True, 
                bufsize=1, 
                universal_newlines=True
            )
            
            if p.stdout:
                for line in p.stdout:
                    self.process_event_line(line.strip())
                
        except KeyboardInterrupt:
            print("\n🛑 监听已停止")
            self.save_session_log()
        except Exception as e:
            print(f"❌ 监听出错: {e}")

    def process_event_line(self, line: str):
        """处理单行事件"""
        if not line:
            return
            
        # 检测触摸开始
        if any(re.search(pat, line) for pat in self.patterns['touch_down']):
            if not self.is_touching:
                self.is_touching = True
                self.touch_start_time = time.time()
                self.touch_positions = []
                
                # 立即截屏 - 操作前状态
                metadata = {
                    "event_type": "touch_start",
                    "timestamp": datetime.now().isoformat(),
                    "activity": self.get_current_activity(),
                    "input_method": self.get_input_method_info()
                }
                self.screencap_now("before_operation", metadata)
        
        # 检测触摸结束
        elif any(re.search(pat, line) for pat in self.patterns['touch_up']):
            if self.is_touching:
                self.is_touching = False
                duration = time.time() - self.touch_start_time if self.touch_start_time else 0
                operation_type = self.analyze_operation_type(self.touch_positions, duration)
                
                # 操作结束后截屏
                metadata = {
                    "event_type": "touch_end",
                    "operation_type": operation_type,
                    "duration": duration,
                    "positions": self.touch_positions,
                    "timestamp": datetime.now().isoformat(),
                    "activity": self.get_current_activity(),
                    "input_method": self.get_input_method_info()
                }
                
                print(f"🔍 检测到操作: {operation_type} (耗时: {duration:.2f}s)")
                
                # 延迟截屏以捕获界面变化
                time.sleep(0.1)
                self.screencap_now("after_operation", metadata)
                
                # 如果是输入操作，额外延迟截屏
                if metadata["input_method"]["input_active"]:
                    time.sleep(0.5)
                    self.screencap_now("after_input", metadata)
                
                # 记录操作日志
                self.operation_log.append(metadata)
        
        # 检测移动
        elif any(re.search(pat, line) for pat in self.patterns['move']):
            if self.is_touching:
                coords = self.extract_coordinates(line)
                if coords:
                    self.touch_positions.append(coords)

    def save_session_log(self):
        """保存会话日志"""
        if self.operation_log:
            log_file = os.path.join(self.output_dir, f"session_{datetime.now().strftime('%Y%m%d_%H%M%S')}.json")
            with open(log_file, 'w', encoding='utf-8') as f:
                json.dump(self.operation_log, f, ensure_ascii=False, indent=2)
            print(f"📋 会话日志已保存: {log_file}")

def main():
    capture = AndroidOperationCapture()
    capture.start_monitoring()

if __name__ == "__main__":
    main()