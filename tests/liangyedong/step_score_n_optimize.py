import sys
import os
# Add project root to Python path
sys.path.insert(0, os.path.join(os.path.dirname(__file__), '..', '..'))

from src.infra.model import get_chat_model
case_1 = """
点击页面底部"我的"切换至我的页面
点击页面顶部最右边"三"图标展开侧边栏
点击"我的形象"进入虚拟形象页面
点击页面左侧"坐姿"按钮切换坐姿至站姿，直至展示“站姿”按钮
"""

case_2 = """
点击页面顶部"娱乐"tab页
点击页面右下方"开启听听"按钮
点击"立即开播"
展示弹窗"达人行为规范"
等待倒计时3秒后点击"我知道了"
进入直播页面后，点击底部左侧一个对话图标
输入'1234569你好！@#¥%'
点击发送
"""

case_3 = """
等待页面右下角的“聊天”按钮出现
点击页面右下角的“聊天”按钮，打开IM聊天半屏
详细描述当前页面最新一条AI回复消息（黑色会话气泡）的最后一行文字  
点击底部输入框
点击输入框下方的图片icon
相册页面勾选任意一张图片
点击页面右下角的确认按钮
点击箭头样式的发送按钮
等待AI回复（黑色会话气泡）
详细描述当前页面最新一条AI回复消息（黑色会话气泡）的最后一行文字（只执行一次）
"""

case_4 = """
点击页面右侧【进入世界】区域
点击除左上角返回按钮外的任意位置
点击除左上角返回按钮外的任意位置
点击底部选项中的【让AI想】
每隔3秒，检查当前弹窗底部是否出现【构建世界】按钮
点击【构建世界】
每隔5秒，检查右上角是否出现【进入】按钮
点击页面右侧【进入】
如果出现【世界观不完整】弹窗
点击弹窗中【进入世界】按钮
"""

user_step_description = case_4

score_system_prompt = """
##### 角色 #####
你是一个测试用例步骤评分助手，你将根据<评分流程> 进行评分，并提示用户需要优化的方向
*<用例步骤>都是用于指导 Agent 进行在手机上执行，所以分数越高执行得越好*

##### 动作列表 #####
* 点击
* 输入
* 滑动
* 拖动
* 长按
* 等待
* 删除
* 回车
* 返回
* 描述
* 失败
* 成功
* 选中

##### 步骤语句描述要素评分 #####
> 总共满分: 10分
**必须要素**
* 动作: 必须包含<动作列表>中的动词或意思相近的同义词 [2.5分]
* 元素特征: 必须包含目标元素的特征描述，如UI组件、文字、颜色或形状等 [2.5分]
**可选要素**
* 【可增强模型定位元素能力】元素位置信息: 包含目标元素的页面绝对位置信息，如页面方位、容器、序号等 [1.5分]
* 【可增强模型定位元素能力】元素相对位置信息: 包含目标元素的相对位置信息，如目标元素的附近元素或目标元素与其他元素的关系等 [1.5分]
* 【可增强模型的理解能力】预期结果状态：包含元素的预期状态，页面展示的描述等；[1分]
* 【可增强模型的理解能力】步骤的作用: 操作该步骤达到的目的；[1分]



##### 评分流程 #####
1. 分析每一句<用例步骤> 是否包含'必须要素' 和 '可选要素'；
2. 为每一步骤进行打分，根据存在的语句中存在的要素进行打分，每种'要素'只允许打分一次，不允许评分超过 10 分；
3. 提示用户缺少的'要素'和其作用，如果该'步骤'达到6分及以上，则不提供优化建议
4. 使用<评分结果输出> 的格式将结果输出


##### 评分结果输出 #####
* 输出以下 json 格式的数据
[
    {{
        "step_sentence": "复述用例步骤内容",
        "score": 评分分数【0-10】分,
        "missing_elements": ["元素特征(optional)","动作(required)"....] // 缺少的要素
        "optimize_advice": "简短的优化建议, 不用过多的描述，主要告诉用户缺失的要素即可"
    }}
]


##### 用例步骤 #####
{user_step_description}


"""

start_score_prompt = """
开始根据 <评分流程> 为 <用例步骤> 的所有步骤，一行是一个步骤，进行评分和提出优化建议
"""


step_optimize_prompt = """
##### 角色 #####
你是一个功能测试用例步骤优化助手，请你针对用例步骤的逻辑进行优化

##### 优化要求 #####
1. 分析每一条用例步骤，重点分析'条件'、'位置'和'操作'是否描述清晰
2. 如果存在描述不清晰或逻辑有误，则改写该步骤，但是意思不能改变
3. 将优化后的用例步骤，按照 <输出要求> 的格式进行输出

##### 输出要求 #####
* 输出以下 json 格式的数据
[
    {{
        "original_sentence": "复述用例步骤内容",
        "optimized_sentence": "优化后的用例步骤内容",
        "is_optimized": "true/false 是否进行了优化"
    }}
]

##### 用例步骤 #####
{user_step_description}
"""
start_optimize_prompt = """
开始根据 <优化要求> 为 <用例步骤> 的所有步骤，一行是一个步骤，进行优化
"""


result_system_prompt = """
##### 角色 #####
你是一个功能测试用例预期结果优化助手，请你针对<预期结果>的逻辑进行优化

##### 优化要求 #####
1. 分析每一条用例结果，重点关注结果的 '状态'描述或'对比'条件:
    - 状态: 主要看<预期结果>描述是否在描述一个结果状态而不是一个动作等；
    - 对比: 主要看<预期结果>是否在比对值或图片描述等；
2. 将优化后的用例结果，按照 <输出要求> 的格式进行输出

##### 输出要求 #####
* 输出以下 json 格式的数据
[
    {{
        "original_sentence": "复述用例步骤内容",
        "optimized_sentence": "优化后的用例步骤内容",
        "is_optimized": "true/false 是否进行了优化"
    }}
]

##### 预期结果 #####
{user_expected_result}
"""

start_result_prompt = """
开始根据 <优化要求> 为 <预期结果> 的内容，进行优化
"""

result_1 = """
更换图片成功image_content_1与image_content_2内容不一致
"""

model = get_chat_model("default")



def hi():
    messages = [
        {"role": "user", "content": "hi, how are you?"}
    ]
    response = model.invoke(messages)
    print(response.content)


def test_score():
    system_prompt = score_system_prompt.format(user_step_description=user_step_description)
    messages = [
        {"role": "system", "content": system_prompt},
        {"role": "user", "content": start_score_prompt}
    ]
    response = model.invoke(messages)
    print(response.content)

def test_optimize():
    system_prompt = step_optimize_prompt.format(user_step_description=user_step_description)
    messages = [
        {"role": "system", "content": system_prompt},
        {"role": "user", "content": start_optimize_prompt}
    ]
    response = model.invoke(messages)
    print(response.content)


def test_result():
    system_prompt = result_system_prompt.format(user_expected_result=result_1)
    messages = [
        {"role": "system", "content": system_prompt},
        {"role": "user", "content": start_result_prompt}
    ]
    response = model.invoke(messages)
    print(response.content)
    

    
if __name__ == "__main__":
    # test_score()
    # test_optimize()
    test_result()
    