#!/usr/bin/env python3
"""
UI-TARS模型服务状态检查脚本

检查UI-TARS模型服务的可用性和基本信息
"""

import sys
import os
import time
import requests
from datetime import datetime

# 添加项目根目录到路径
sys.path.append(os.path.dirname(os.path.dirname(os.path.abspath(__file__))))

from loguru import logger
from config.globalconfig import get_or_create_settings_ins


def check_service_health():
    """检查服务健康状态"""
    logger.info("🏥 检查UI-TARS服务健康状态...")
    
    try:
        # 获取配置
        config = get_or_create_settings_ins()
        ui_tars_config = config.llms.get("ui_tars")
        
        if not ui_tars_config:
            logger.error("❌ 未找到ui_tars模型配置")
            return False
        
        base_url = ui_tars_config.external_args.get("base_url")
        api_key = ui_tars_config.external_args.get("api_key")
        
        logger.info(f"📋 服务配置:")
        logger.info(f"   基础URL: {base_url}")
        logger.info(f"   模型名称: {ui_tars_config.model}")
        
        # 1. 检查基础URL连通性
        logger.info("\n🌐 检查基础URL连通性...")
        try:
            # 移除/v1后缀来检查基础服务
            base_check_url = base_url.replace("/v1", "")
            response = requests.get(base_check_url, timeout=10)
            logger.info(f"✅ 基础URL状态码: {response.status_code}")
        except Exception as e:
            logger.warning(f"⚠️ 基础URL检查失败: {e}")
        
        # 2. 检查健康检查端点
        logger.info("\n💓 检查健康检查端点...")
        health_endpoints = [
            f"{base_url}/health",
            f"{base_url}/v1/health", 
            f"{base_check_url}/health",
            f"{base_check_url}/api/health"
        ]
        
        for endpoint in health_endpoints:
            try:
                response = requests.get(endpoint, timeout=5)
                logger.info(f"✅ {endpoint} - 状态码: {response.status_code}")
                if response.status_code == 200:
                    try:
                        data = response.json()
                        logger.info(f"   响应数据: {data}")
                    except:
                        logger.info(f"   响应内容: {response.text[:100]}...")
                    break
            except Exception as e:
                logger.debug(f"   {endpoint} - 失败: {e}")
        
        # 3. 检查模型列表端点
        logger.info("\n📋 检查模型列表...")
        try:
            models_url = f"{base_url}/models"
            headers = {"Authorization": f"Bearer {api_key}"}
            response = requests.get(models_url, headers=headers, timeout=10)
            logger.info(f"✅ 模型列表状态码: {response.status_code}")
            
            if response.status_code == 200:
                try:
                    data = response.json()
                    if "data" in data:
                        models = data["data"]
                        logger.info(f"   可用模型数量: {len(models)}")
                        for model in models[:3]:  # 显示前3个模型
                            logger.info(f"   - {model.get('id', 'Unknown')}")
                        if len(models) > 3:
                            logger.info(f"   ... 还有 {len(models) - 3} 个模型")
                    else:
                        logger.info(f"   响应: {data}")
                except Exception as e:
                    logger.info(f"   响应解析失败: {e}")
                    logger.info(f"   原始响应: {response.text[:200]}...")
            else:
                logger.warning(f"   响应内容: {response.text[:200]}...")
                
        except Exception as e:
            logger.warning(f"⚠️ 模型列表检查失败: {e}")
        
        # 4. 测试简单的聊天完成请求
        logger.info("\n💬 测试简单聊天请求...")
        try:
            chat_url = f"{base_url}/chat/completions"
            headers = {
                "Authorization": f"Bearer {api_key}",
                "Content-Type": "application/json"
            }
            
            payload = {
                "model": ui_tars_config.model,
                "messages": [
                    {"role": "user", "content": "Hello"}
                ],
                "max_tokens": 10,
                "temperature": 0
            }
            
            start_time = time.time()
            response = requests.post(chat_url, json=payload, headers=headers, timeout=30)
            end_time = time.time()
            
            logger.info(f"✅ 聊天请求状态码: {response.status_code}")
            logger.info(f"✅ 响应时间: {end_time - start_time:.2f}秒")
            
            if response.status_code == 200:
                try:
                    data = response.json()
                    if "choices" in data and len(data["choices"]) > 0:
                        content = data["choices"][0]["message"]["content"]
                        logger.info(f"✅ 模型响应: {content}")
                        logger.info("🎉 UI-TARS服务完全正常！")
                        return True
                    else:
                        logger.warning(f"⚠️ 响应格式异常: {data}")
                except Exception as e:
                    logger.warning(f"⚠️ 响应解析失败: {e}")
                    logger.info(f"   原始响应: {response.text[:200]}...")
            else:
                logger.error(f"❌ 聊天请求失败")
                logger.error(f"   状态码: {response.status_code}")
                logger.error(f"   响应: {response.text[:300]}...")
                
        except Exception as e:
            logger.error(f"❌ 聊天请求异常: {e}")
        
        return False
        
    except Exception as e:
        logger.error(f"❌ 服务检查失败: {str(e)}")
        return False


def check_network_connectivity():
    """检查网络连通性"""
    logger.info("🌍 检查网络连通性...")
    
    test_urls = [
        "https://www.baidu.com",
        "https://www.google.com", 
        "http://ray.ttyuyin.com:10001"
    ]
    
    for url in test_urls:
        try:
            response = requests.get(url, timeout=5)
            logger.info(f"✅ {url} - 状态码: {response.status_code}")
        except Exception as e:
            logger.warning(f"⚠️ {url} - 失败: {e}")


def main():
    """主函数"""
    logger.info("=" * 60)
    logger.info("UI-TARS模型服务状态检查")
    logger.info(f"检查时间: {datetime.now().strftime('%Y-%m-%d %H:%M:%S')}")
    logger.info("=" * 60)
    
    # 检查网络连通性
    check_network_connectivity()
    
    logger.info("\n" + "=" * 60)
    
    # 检查服务状态
    success = check_service_health()
    
    logger.info("\n" + "=" * 60)
    if success:
        logger.info("🎯 检查结果: 服务正常 ✅")
        logger.info("💡 建议: 可以运行完整的模型测试")
        sys.exit(0)
    else:
        logger.info("🎯 检查结果: 服务异常 ❌")
        logger.info("💡 建议:")
        logger.info("   1. 检查服务器是否正在运行")
        logger.info("   2. 确认网络连接是否正常")
        logger.info("   3. 验证API密钥和URL配置")
        logger.info("   4. 联系服务管理员检查服务状态")
        sys.exit(1)


if __name__ == "__main__":
    main()
