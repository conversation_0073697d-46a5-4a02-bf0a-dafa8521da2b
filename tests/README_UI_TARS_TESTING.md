# UI-TARS模型测试指南

本目录包含用于测试ByteDance-Seed/UI-TARS-1.5-7B模型的测试脚本。

## 测试脚本说明

### 1. 完整测试脚本 (`test_ui_tars_model.py`)

这是一个全面的测试脚本，包含以下测试项目：

- **基本连接测试**: 验证模型API连接是否正常
- **文本生成测试**: 测试模型的基本文本生成能力
- **UI理解能力测试**: 测试模型对UI元素的理解能力
- **坐标模型集成测试**: 测试作为坐标模型的集成功能
- **性能测试**: 测试响应时间和成功率

#### 运行方式:
```bash
# 在项目根目录下运行
cd /Users/<USER>/Desktop/TT/code/click-pilot
/Users/<USER>/Desktop/TT/code/click-pilot/.venv/bin/python tests/test_ui_tars_model.py
```

#### 输出:
- 控制台实时日志
- 详细的JSON测试报告 (`tests/ui_tars_test_report_YYYYMMDD_HHMMSS.json`)

### 2. 快速测试脚本 (`quick_test_ui_tars.py`)

这是一个简化的快速测试脚本，用于验证模型基本功能：

- 基本连接测试
- UI理解能力测试
- 坐标理解能力测试
- 流式响应测试

#### 运行方式:
```bash
# 在项目根目录下运行
cd /Users/<USER>/Desktop/TT/code/click-pilot
/Users/<USER>/Desktop/TT/code/click-pilot/.venv/bin/python tests/quick_test_ui_tars.py
```

## 模型配置

测试脚本使用项目配置文件中的UI-TARS模型配置：

```toml
[llms.ui_tars]
provider = "openai"
temperature = 0.2
model = "ByteDance-Seed/UI-TARS-1.5-7B"
external_args = { 
    base_url = "http://ray.ttyuyin.com:10001/vllm-ui-tars-7b/v1", 
    api_key = "sk-quwan-jarvis" 
}
```

## 测试结果解读

### 成功标准

- **基本连接**: 能够成功连接并获得响应
- **文本生成**: 响应长度 > 10字符，不以"Error"开头
- **UI理解**: 响应包含至少50%的期望关键词
- **性能**: 平均响应时间 < 10秒，成功率 ≥ 80%

### 测试报告

完整测试会生成JSON格式的详细报告，包含：

```json
{
  "total_tests": 5,
  "passed_tests": 4,
  "failed_tests": 1,
  "success_rate": 0.8,
  "total_time": 45.67,
  "model_config": {
    "model_name": "ByteDance-Seed/UI-TARS-1.5-7B",
    "temperature": 0.2,
    "base_url": "http://ray.ttyuyin.com:10001/vllm-ui-tars-7b/v1"
  },
  "detailed_results": [...]
}
```

## 故障排除

### 常见问题

1. **连接失败**
   - 检查网络连接
   - 验证base_url是否可访问
   - 确认API密钥是否正确

2. **导入错误**
   - 确保在项目根目录运行
   - 检查Python环境是否正确激活
   - 验证依赖包是否安装

3. **配置错误**
   - 检查config/no_prod.toml文件
   - 确认ui_tars配置是否存在
   - 验证配置格式是否正确

### 调试模式

可以通过设置环境变量启用详细日志：

```bash
export LOG_LEVEL=DEBUG
/Users/<USER>/Desktop/TT/code/click-pilot/.venv/bin/python tests/quick_test_ui_tars.py
```

## 性能基准

基于测试结果，UI-TARS模型的预期性能：

- **响应时间**: 通常在2-8秒之间
- **成功率**: 应该 ≥ 90%
- **UI理解准确性**: 关键词匹配率 ≥ 70%

## 扩展测试

如需添加更多测试用例，可以：

1. 在`UITarsModelTester`类中添加新的测试方法
2. 在`run_all_tests`方法中注册新测试
3. 更新测试标准和评估逻辑

## 注意事项

- 测试会产生API调用费用（如果适用）
- 某些测试可能需要较长时间完成
- 网络状况会影响测试结果
- 建议在稳定的网络环境下运行测试
