#!/usr/bin/env python3
"""
UI-TARS模型快速测试脚本

快速验证UI-TARS模型是否正常工作
"""

import sys
import os
import time
from datetime import datetime

# 添加项目根目录到路径
sys.path.append(os.path.dirname(os.path.dirname(os.path.abspath(__file__))))

from openai import OpenAI
from loguru import logger
from config.globalconfig import get_or_create_settings_ins


def test_ui_tars_model():
    """快速测试UI-TARS模型"""
    logger.info("🚀 开始UI-TARS模型快速测试...")

    try:
        # 获取配置
        config = get_or_create_settings_ins()
        ui_tars_config = config.llms.get("ui_tars")

        if not ui_tars_config:
            logger.error("❌ 未找到ui_tars模型配置")
            return False

        # 显示配置信息
        logger.info(f"📋 模型配置:")
        logger.info(f"   模型名称: {ui_tars_config.model}")
        logger.info(f"   基础URL: {ui_tars_config.external_args.get('base_url')}")
        logger.info(f"   温度: {ui_tars_config.temperature}")

        # 初始化客户端
        client = OpenAI(
            base_url=ui_tars_config.external_args.get("base_url"),
            api_key=ui_tars_config.external_args.get("api_key"),
            timeout=30.0  # 设置超时时间
        )
        
        # 测试1: 基本连接（带重试机制）
        logger.info("\n🔗 测试1: 基本连接...")

        max_retries = 3
        for attempt in range(max_retries):
            try:
                logger.info(f"   尝试 {attempt + 1}/{max_retries}...")
                start_time = time.time()

                response = client.chat.completions.create(
                    model=ui_tars_config.model,
                    messages=[
                        {"role": "user", "content": "Hello! Please respond with 'UI-TARS model is working correctly.'"}
                    ],
                    temperature=0,
                    max_tokens=4096
                )

                end_time = time.time()
                response_time = end_time - start_time
                content = response.choices[0].message.content

                logger.info(f"✅ 响应时间: {response_time:.2f}秒")
                logger.info(f"📝 响应内容: {content}")
                break  # 成功则跳出重试循环

            except Exception as e:
                logger.warning(f"   尝试 {attempt + 1} 失败: {str(e)}")
                if attempt == max_retries - 1:
                    logger.error(f"❌ 基本连接测试失败，已重试 {max_retries} 次")
                    logger.error(f"   最后错误: {str(e)}")
                    logger.info("💡 可能的原因:")
                    logger.info("   - 服务器暂时不可用 (502 错误)")
                    logger.info("   - 网络连接问题")
                    logger.info("   - API密钥或URL配置错误")
                    logger.info("   - 服务器负载过高")
                    return False
                else:
                    time.sleep(2)  # 等待2秒后重试
        
        # 测试2: UI相关任务
        logger.info("\n🖥️ 测试2: UI理解能力...")
        start_time = time.time()
        
        ui_prompt = """
        我有一个移动应用界面，包含以下元素：
        - 顶部有一个标题"登录页面"
        - 中间有两个输入框：用户名和密码
        - 底部有一个蓝色的"登录"按钮
        
        如果我想要点击登录按钮，请描述我应该寻找什么样的UI元素特征。
        """
        
        response = client.chat.completions.create(
            model=ui_tars_config.model,
            messages=[
                {"role": "user", "content": ui_prompt}
            ],
            temperature=ui_tars_config.temperature,
            max_tokens=200
        )
        
        end_time = time.time()
        response_time = end_time - start_time
        content = response.choices[0].message.content
        
        logger.info(f"✅ 响应时间: {response_time:.2f}秒")
        logger.info(f"📝 响应内容: {content[:200]}...")
        
        # 测试3: 坐标相关任务
        logger.info("\n🎯 测试3: 坐标理解能力...")
        start_time = time.time()
        
        coordinate_prompt = """
        在一个1080x1920像素的手机屏幕上，有一个"确认"按钮位于屏幕底部中央。
        假设按钮宽度为200像素，高度为60像素。
        请估算这个按钮的中心点坐标。
        """
        
        response = client.chat.completions.create(
            model=ui_tars_config.model,
            messages=[
                {"role": "user", "content": coordinate_prompt}
            ],
            temperature=0,
            max_tokens=150
        )
        
        end_time = time.time()
        response_time = end_time - start_time
        content = response.choices[0].message.content
        
        logger.info(f"✅ 响应时间: {response_time:.2f}秒")
        logger.info(f"📝 响应内容: {content}")
        
        # 测试4: 流式响应
        logger.info("\n🌊 测试4: 流式响应...")
        start_time = time.time()
        
        stream_response = client.chat.completions.create(
            model=ui_tars_config.model,
            messages=[
                {"role": "user", "content": "请简单介绍一下UI自动化测试的基本概念。"}
            ],
            temperature=ui_tars_config.temperature,
            max_tokens=200,
            stream=True
        )
        
        full_response = ""
        for chunk in stream_response:
            if chunk.choices[0].delta.content:
                content_chunk = chunk.choices[0].delta.content
                full_response += content_chunk
                print(content_chunk, end="", flush=True)
        
        end_time = time.time()
        response_time = end_time - start_time
        
        print()  # 换行
        logger.info(f"✅ 流式响应时间: {response_time:.2f}秒")
        logger.info(f"📝 完整响应长度: {len(full_response)}字符")
        
        logger.info("\n🎉 所有测试完成！UI-TARS模型工作正常。")
        return True
        
    except Exception as e:
        logger.error(f"❌ 测试失败: {str(e)}")
        return False


def main():
    """主函数"""
    logger.info("=" * 60)
    logger.info("UI-TARS模型快速测试")
    logger.info(f"测试时间: {datetime.now().strftime('%Y-%m-%d %H:%M:%S')}")
    logger.info("=" * 60)
    
    success = test_ui_tars_model()
    
    logger.info("\n" + "=" * 60)
    if success:
        logger.info("🎯 测试结果: 成功 ✅")
        sys.exit(0)
    else:
        logger.info("🎯 测试结果: 失败 ❌")
        sys.exit(1)


if __name__ == "__main__":
    main()
