import typing

from pydantic import BaseModel

from src.infra.web import const

__all__ = ["HttpResponse"]

class HttpResponse(BaseModel):
    code: int = const.CODE_SUCCEED
    message: str = ""
    data: typing.Optional[typing.Any] = None

    def error_response(self, message: str = "", code: int = const.CODE_ERROR) -> str:
        self.code = code
        self.message = message
        return self.model_dump_json()
