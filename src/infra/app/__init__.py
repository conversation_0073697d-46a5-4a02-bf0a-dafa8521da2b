import os
import time
from concurrent.futures import Thread<PERSON>oolExecutor, Future

from loguru import logger

from config.globalconfig import GlobalConfig, get_or_create_settings_ins
from src.infra.clients.mysql.orm import (
    init_db_engine,
    init_db_session,
    DBSessionContext,
)
from src.infra.log import init_logger
from src.infra.scheduler import app_scheduler
from src.infra.utils import get_current_env
from src.infra.web.http import http_server

init_logger()


class App:

    def __init__(self, config: GlobalConfig):
        self.config = config
        self.thread_pool: ThreadPoolExecutor = ThreadPoolExecutor(
            max_workers=self.config.app.thread_pool_size
        )

        self.logger = logger
        self.http_server = http_server
        self.scheduler = app_scheduler

        self._on_starts = []
        self._on_stops = []

        self._init()

    @property
    def current_env(self):
        return get_current_env()

    def _init_mysql(self):
        self._db_engine = init_db_engine(self.config)
        if self._db_engine is None:
            raise Exception("mysql engine is None")
        init_db_session(self._db_engine)

    def _on_start_with_thread(self) -> None:
        if self.thread_pool is None:
            raise ValueError("thread pool is not initialized")

        def function_wrapper(func):
            try:
                func()
            except Exception as e:
                logger.exception(f"Error in thread pool: {e}")

        for func in self._on_starts:
            self.thread_pool.submit(function_wrapper, func)

    def _on_stop_with_thread(self) -> None:
        if self.thread_pool is None:
            raise ValueError("thread pool is not initialized")

        ## func 需要 try catch
        def function_wrapper(func):
            try:
                func()
            except Exception as e:
                logger.exception(f"Error in thread pool: {e}")

        for func in self._on_stops:
            self.thread_pool.submit(function_wrapper, func)

        if len(self._on_stops) > 0:
            logger.info("Waiting 3 seconds for the cleanup thread...")
            time.sleep(3)

    def _init(self):
        self._init_mysql()

    def orm_session(self) -> DBSessionContext:
        """with genius.db_session() as db:
        db.query.xxx
        """
        return DBSessionContext()

    def submit_concurrent_task(self, func: callable, *args, **kwargs) -> Future:
        def wrap_func(func, *args, **kwargs):
            try:
                return func(*args, **kwargs)
            except Exception as e:
                logger.exception(f"concurrent task error: {e}")

        return self.thread_pool.submit(wrap_func, func, *args, **kwargs)

    def add_on_start(self, func: callable) -> None:
        self._on_starts.append(func)

    def add_on_stop(self, func: callable) -> None:
        self._on_stops.append(func)

    def launch(self):
        self.http_server.add_event_handler("startup", self._on_start_with_thread)
        self.http_server.add_event_handler("shutdown", self._on_stop_with_thread)
        self.http_server.launch()
        os._exit(0)


app = App(config=get_or_create_settings_ins())
