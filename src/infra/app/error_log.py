from enum import Enum

from loguru import logger
from sqlalchemy import Column, VARCHAR, Text

from src.infra.app import app
from src.infra.clients.mysql.orm import (
    IdModelMixin,
    BaseModel,
    DateTimeModelMixin,
    TablePrefix,
)
from src.infra.utils import retry_custom


class ErrorLog(IdModelMixin, DateTimeModelMixin):
    
    __tablename__ = f"{TablePrefix}_error_log"
    
    error_name = Column(VARCHAR(128), nullable=False, index=True, comment="错误名称")
    error_message = Column(VARCHAR(2048), nullable=False, comment="错误信息")
    error_data = Column(Text, nullable=False, comment="错误数据")
    

class ErrorName(str, Enum):
    ZH_STOCK_SYNC_ERROR = "zh_stock_sync_error"

@retry_custom
def record_zh_stock_error(error_message: str, error_data: str):
    try:
        with app.orm_session() as session:
            error_log = ErrorLog(
                error_name=ErrorName.ZH_STOCK_SYNC_ERROR.value,
                error_message=error_message,
                error_data=error_data,
            )
            session.add(error_log)
            session.commit()
    except Exception as e:
        logger.exception(f"记录错误日志时出错: {e}")
