import asyncio
import threading
import uuid
import time
from concurrent.futures import Future, ThreadPoolExecutor
from enum import Enum, auto
from typing import Any, Callable, Dict, List, Optional, Tuple, Union

from loguru import logger
from pydantic import BaseModel, Field

from src.infra.utils import ThreadSafeDict, get_event_loop


class EventType(Enum):
    """事件类型枚举"""
    STORE_UPDATED = auto()
    DATA_PROCESSED = auto()
    NOTIFICATION = auto()
    # 添加更多事件类型...



class Event(BaseModel):
    """事件数据类"""
    type: EventType = Field(..., description="事件类型")
    data: Dict[Any, Any] = Field(..., description="事件数据")
    source: str = Field(..., description="事件源")
    id: str = Field(default_factory=lambda: str(uuid.uuid4()), description="事件ID")

    def __post_init__(self):
        if not self.id:
            self.id = str(uuid.uuid4())


class EventBus:
    """事件总线，处理事件的发布和订阅"""

    _instance = None
    _lock = threading.RLock()
    _event_loop = None
    _loop_thread = None
    _running = False

    def __new__(cls):
        with cls._lock:
            if cls._instance is None:
                cls._instance = super(EventBus, cls).__new__(cls)
                cls._instance._initialized = False
            return cls._instance

    def __init__(self):
        if self._initialized:
            return

        self._subscribers = ThreadSafeDict()  # {event_type: [callbacks]}
        self._waiting_futures = ThreadSafeDict()  # {event_id: Future}
        self._initialized = True
        # 初始化时不自动启动事件循环，由start方法显式启动
        # self._ensure_event_loop()
        EventBus._running = False

    def _ensure_event_loop(self):
        """确保事件循环在运行"""
        if EventBus._event_loop is None:
            def run_event_loop():
                loop = asyncio.new_event_loop()
                asyncio.set_event_loop(loop)
                EventBus._event_loop = loop
                try:
                    loop.run_forever()
                finally:
                    loop.close()
                    EventBus._event_loop = None
                    EventBus._running = False

            # 启动事件循环线程
            EventBus._loop_thread = threading.Thread(
                target=run_event_loop,
                daemon=True,
                name="EventBusEventLoop"
            )
            EventBus._loop_thread.start()

            # 等待事件循环初始化完成
            while EventBus._event_loop is None:
                time.sleep(0.01)

            EventBus._running = True

    def subscribe(self, event_type: EventType, callback: Callable[[Event], Any]) -> None:
        """订阅特定类型的事件"""
        if event_type not in self._subscribers:
            self._subscribers[event_type] = []
        self._subscribers[event_type].append(callback)

    def unsubscribe(self, event_type: EventType, callback: Callable[[Event], Any]) -> None:
        """取消订阅特定类型的事件"""
        if event_type in self._subscribers and callback in self._subscribers[event_type]:
            self._subscribers[event_type].remove(callback)

    def publish(self, event: Event) -> None:
        """异步发布事件，不等待结果"""
        try:
            if not EventBus._running or EventBus._event_loop is None:
                logger.warning("EventBus is not running. Call start() before publishing events.")
                self.start()  # 自动启动事件总线

            asyncio.run_coroutine_threadsafe(self._process_event(event), EventBus._event_loop)
        except Exception as e:
            logger.error(f"Error publishing event: {e}")

    def publish_sync(self, event: Event, timeout: float = 10.0) -> Dict[str, Any]:
        """同步发布事件，等待结果"""
        if not EventBus._running or EventBus._event_loop is None:
            logger.warning("EventBus is not running. Call start() before publishing events.")
            self.start()  # 自动启动事件总线

        future = Future()
        self._waiting_futures[event.id] = future

        try:
            asyncio.run_coroutine_threadsafe(self._process_event(event), EventBus._event_loop)
            return future.result(timeout=timeout)
        except Exception as e:
            logger.error(f"Error in sync event publishing: {e}")
            if event.id in self._waiting_futures:
                del self._waiting_futures[event.id]
            raise e

    async def _process_event(self, event: Event) -> None:
        """处理事件并通知订阅者"""
        results = []

        if event.type in self._subscribers:
            for callback in self._subscribers[event.type]:
                try:
                    result = callback(event)
                    # 处理异步回调
                    if asyncio.iscoroutine(result):
                        result = await result
                    results.append(result)
                except Exception as e:
                    logger.error(f"Error in event handler: {e}")

        # 如果是同步等待的事件，设置结果
        if event.id in self._waiting_futures:
            future = self._waiting_futures[event.id]
            if not future.done():
                # 返回最后一个处理器的结果
                future.set_result(results[-1] if results else None)
            del self._waiting_futures[event.id]

    def start(self) -> None:
        """启动事件总线

        可以在应用启动时调用，用于app.add_on_start注册
        """
        with self._lock:
            if not EventBus._running:
                logger.info("Starting EventBus...")
                self._ensure_event_loop()
                logger.info("EventBus started successfully")
            else:
                logger.info("EventBus is already running")

    def stop(self) -> None:
        """停止事件总线

        可以在应用关闭时调用，用于app.add_on_stop注册
        """
        with self._lock:
            if EventBus._running and EventBus._event_loop is not None:
                logger.info("Stopping EventBus...")

                # 取消所有等待中的future
                for event_id, future in list(self._waiting_futures.items()):
                    if not future.done():
                        future.cancel()
                self._waiting_futures.clear()

                # 停止事件循环
                try:
                    # 使用utils中的安全关闭方法
                    from src.infra.utils import safe_close_event_loop

                    # 在事件循环线程中执行关闭操作
                    if EventBus._event_loop and not EventBus._event_loop.is_closed():
                        EventBus._event_loop.call_soon_threadsafe(safe_close_event_loop)

                        # 等待事件循环关闭
                        timeout = 5  # 最多等待5秒
                        start_time = time.time()
                        while EventBus._running and time.time() - start_time < timeout:
                            time.sleep(0.1)

                    logger.info("EventBus stopped successfully")
                except Exception as e:
                    logger.error(f"Error stopping EventBus: {e}")
            else:
                logger.info("EventBus is not running")


# 全局事件总线实例
event_bus = EventBus()


# 使用示例
if __name__ == "__main__":
    # 1. 定义事件处理函数
    def handle_store_updated(event: Event):
        print(f"处理存储更新事件: {event.data}")
        return {"status": "success", "message": "存储已更新"}

    def handle_notification(event: Event):
        print(f"发送通知: {event.data}")
        return {"status": "sent"}

    async def async_handle_data_processed(event: Event):
        print(f"异步处理数据: {event.data}")
        await asyncio.sleep(1)  # 模拟异步操作
        return {"status": "processed", "data_id": 12345}

    # 2. 显式启动事件总线
    print("启动事件总线...")
    event_bus.start()

    # 3. 订阅事件
    event_bus.subscribe(EventType.STORE_UPDATED, handle_store_updated)
    event_bus.subscribe(EventType.NOTIFICATION, handle_notification)
    event_bus.subscribe(EventType.DATA_PROCESSED, async_handle_data_processed)

    # 4. 异步发布事件（不等待结果）
    store_event = Event(
        type=EventType.STORE_UPDATED,
        data={"item_id": 123, "new_value": "updated"},
        source="example"
    )
    event_bus.publish(store_event)

    # 5. 同步发布事件（等待结果）
    data_event = Event(
        type=EventType.DATA_PROCESSED,
        data={"dataset": "stocks", "action": "process"},
        source="example"
    )
    try:
        # 同步等待结果
        result = event_bus.publish_sync(data_event)
        print(f"同步事件处理结果: {result}")
    except Exception as e:
        print(f"同步事件处理出错: {e}")

    # 6. 发送多个事件测试
    for i in range(3):
        event = Event(
            type=EventType.NOTIFICATION,
            data={"message": f"测试消息 {i}"},
            source="example"
        )
        result = event_bus.publish_sync(event)
        print(f"通知事件 {i} 结果: {result}")

    print("所有事件已处理完成")

    # 7. 等待一段时间确保异步事件被处理
    time.sleep(2)

    # 8. 停止事件总线
    print("停止事件总线...")
    event_bus.stop()
    print("事件总线已停止")