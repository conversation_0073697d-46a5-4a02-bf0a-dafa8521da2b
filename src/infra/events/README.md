# 事件总线 (EventBus) 使用指南

事件总线是一个用于处理应用内部事件发布和订阅的组件，支持同步和异步事件处理。

## 基本用法

### 1. 导入事件总线

```python
from src.infra.events import event_bus, Event, EventType
```

### 2. 在应用启动和关闭时注册事件总线

在应用的主入口文件（如 `main.py`）中，将事件总线的启动和停止方法注册到应用的生命周期钩子中：

```python
from src.infra.app import app
from src.infra.events import event_bus

# 注册事件总线启动
app.add_on_start(event_bus.start)

# 注册事件总线停止
app.add_on_stop(event_bus.stop)

# 启动应用
app.launch()
```

### 3. 定义事件处理函数

```python
def handle_store_updated(event: Event):
    print(f"处理存储更新事件: {event.data}")
    return {"status": "success", "message": "存储已更新"}

# 支持异步处理函数
async def async_handle_data_processed(event: Event):
    print(f"异步处理数据: {event.data}")
    await asyncio.sleep(1)  # 异步操作
    return {"status": "processed", "data_id": 12345}
```

### 4. 订阅事件

```python
event_bus.subscribe(EventType.STORE_UPDATED, handle_store_updated)
event_bus.subscribe(EventType.DATA_PROCESSED, async_handle_data_processed)
```

### 5. 发布事件

#### 异步发布（不等待结果）

```python
store_event = Event(
    type=EventType.STORE_UPDATED,
    data={"item_id": 123, "new_value": "updated"},
    source="example"
)
event_bus.publish(store_event)
```

#### 同步发布（等待结果）

```python
data_event = Event(
    type=EventType.DATA_PROCESSED,
    data={"dataset": "stocks", "action": "process"},
    source="example"
)
try:
    # 同步等待结果，可设置超时时间
    result = event_bus.publish_sync(data_event, timeout=5.0)
    print(f"同步事件处理结果: {result}")
except Exception as e:
    print(f"同步事件处理出错: {e}")
```

## 高级用法

### 自定义事件类型

在 `EventType` 枚举中添加新的事件类型：

```python
class EventType(Enum):
    """事件类型枚举"""
    STORE_UPDATED = auto()
    DATA_PROCESSED = auto()
    NOTIFICATION = auto()
    # 添加自定义事件类型
    USER_LOGGED_IN = auto()
    ORDER_CREATED = auto()
```

### 取消订阅

```python
event_bus.unsubscribe(EventType.STORE_UPDATED, handle_store_updated)
```

## 注意事项

1. 事件总线使用单例模式，全局只有一个实例
2. 事件处理函数可以是同步或异步的
3. 事件总线会在应用启动时自动启动，在应用关闭时自动停止
4. 如果在事件总线未启动时发布事件，会自动启动事件总线
5. 事件总线使用线程安全的数据结构，可以在多线程环境中安全使用
