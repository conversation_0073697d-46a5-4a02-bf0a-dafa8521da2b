import asyncio
import os
from functools import cache
from typing import Any, Union, Tuple, Optional, Type

import requests
from loguru import logger
from tenacity import retry, stop_after_attempt, wait_fixed, retry_if_exception_type

from src.schema import env, const

__all__ = [
    "get_work_dir",
    "get_current_version",
    "get_event_loop",
    "safe_close_event_loop",
    "ThreadSafeDict",
    "get_current_env",
]


@cache
def get_current_env() -> str:
    """
    获取当前环境
    """
    return os.environ.get(env.RUNTIME_ENV) or const.NO_PROD_ENV


@cache
def get_work_dir() -> str:
    """
    获取工作目录
    """
    return os.environ.get(env.WORK_DIR) or os.getcwd()


@cache
def get_current_version() -> str:
    """
    获取当前版本
    """
    return os.environ.get(env.VERSION) or "0.0.0"


def get_event_loop() -> asyncio.AbstractEventLoop:
    try:
        asyncio.set_event_loop_policy(asyncio.DefaultEventLoopPolicy())
        return asyncio.get_running_loop()
    except RuntimeError:
        loop = asyncio.new_event_loop()
        asyncio.set_event_loop(loop)
        return loop


def safe_close_event_loop() -> None:
    try:
        loop = asyncio.get_running_loop()
    except RuntimeError:
        return  # 没有运行中的事件循环

    if loop.is_closed():
        return  # 循环已关闭

    # 取消所有任务
    tasks = [t for t in asyncio.all_tasks(loop) if not t.done()]
    if not tasks:
        return

    for task in tasks:
        task.cancel()

    # 安排停止和关闭
    def stop_loop() -> None:
        try:
            # 收集取消后的任务异常
            loop.run_until_complete(asyncio.gather(*tasks, return_exceptions=True))
            # 确保资源释放
            loop.run_until_complete(loop.shutdown_asyncgens())
            loop.run_until_complete(loop.shutdown_default_executor())

            loop.stop()
            # 只有在不是由 asyncio.run() 启动时才手动关闭
            if not isinstance(getattr(loop, "_closed", False), bool):
                loop.close()
        except Exception as e:
            print(f"Error during loop shutdown: {e}")

    try:
        loop.call_soon_threadsafe(stop_loop)
    except Exception as e:
        print(f"Failed to stop event loop: {e}")


def create_retry_decorator(
    max_retries: int = 1,
    wait_seconds: Union[int, float] = 3,
    retry_exceptions: Union[
        Type[Exception], Tuple[Type[Exception], ...]
    ] = requests.exceptions.RequestException,
    before_sleep_log: Optional[callable] = None,
):
    """
    创建预配置的Tenacity重试装饰器

    :param max_retries: 最大重试次数（不含首次请求）
    :param wait_seconds: 重试等待时间（秒）
    :param retry_exceptions: 需要重试的异常类型
    :param before_sleep_log: 自定义日志函数（默认打印）
    """

    def _before_sleep_default(retry_state):
        msg = (
            f"请求失败 → 第{retry_state.attempt_number}次重试 | "
            f"等待{wait_seconds}秒 | 异常: {retry_state.outcome.exception()}"
        )
        print(msg) if before_sleep_log is None else before_sleep_log(msg)

    return retry(
        stop=stop_after_attempt(max_retries + 1),  # 总尝试次数 = 原始调用 + max_retries
        wait=wait_fixed(wait_seconds),
        retry=retry_if_exception_type(retry_exceptions),
        before_sleep=_before_sleep_default,  # 内置日志整合
    )


# 创建默认重试装饰器（2次重试，间隔3秒，针对网络异常）
retry_network = create_retry_decorator(max_retries=3)

# 自定义配置的装饰器（生产环境推荐）
retry_custom = create_retry_decorator(
    max_retries=5,
    wait_seconds=2,
    retry_exceptions=(requests.exceptions.RequestException, OSError),
    before_sleep_log=logger.warning,
)

import threading
from collections.abc import MutableMapping


class ThreadSafeDict(MutableMapping):
    """线程安全的字典实现，支持大多数标准字典操作"""

    def __init__(self, *args, **kwargs):
        self._dict = dict(*args, **kwargs)
        self._lock = threading.RLock()  # 使用可重入锁，避免死锁

    def __getitem__(self, key):
        with self._lock:
            return self._dict[key]

    def __setitem__(self, key, value):
        with self._lock:
            self._dict[key] = value
            # print(f"ThreadSafeDict: Set {key} = {value}")

    def __delitem__(self, key):
        with self._lock:
            del self._dict[key]

    def __iter__(self):
        with self._lock:
            return iter(self._dict.copy())  # 返回副本的迭代器

    def __len__(self):
        with self._lock:
            return len(self._dict)

    def __repr__(self):
        with self._lock:
            return f"ThreadSafeDict({self._dict!r})"

    def get(self, key, default=None):
        with self._lock:
            return self._dict.get(key, default)

    def keys(self):
        with self._lock:
            return list(self._dict.keys())

    def values(self):
        with self._lock:
            return list(self._dict.values())

    def items(self):
        with self._lock:
            return list(self._dict.items())

    def pop(self, key, default=None):
        with self._lock:
            return self._dict.pop(key, default)

    def popitem(self):
        with self._lock:
            return self._dict.popitem()

    def clear(self):
        with self._lock:
            self._dict.clear()

    def update(self, *args, **kwargs):
        with self._lock:
            self._dict.update(*args, **kwargs)

    def setdefault(self, key, default=None):
        with self._lock:
            return self._dict.setdefault(key, default)

    def copy(self):
        with self._lock:
            return ThreadSafeDict(self._dict.copy())

    def __contains__(self, key):
        with self._lock:
            return key in self._dict

    def __eq__(self, other):
        with self._lock:
            if isinstance(other, ThreadSafeDict):
                with other._lock:
                    return self._dict == other._dict
            return self._dict == other

    # 原子操作方法
    def get_or_set(self, key, default_value) -> Any:
        """原子操作：获取键值，如果不存在则设置默认值"""
        with self._lock:
            if key not in self._dict:
                self._dict[key] = default_value
            return self._dict[key]
