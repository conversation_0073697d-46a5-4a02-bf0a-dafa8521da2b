from enum import Enum

import lark_oapi as lark

from config.globalconfig import get_or_create_settings_ins

config = get_or_create_settings_ins()


class LarkClientType(str, Enum):
    tt = "tt"
    trading = "trading"


def get_lark_client(client_type: LarkClientType) -> lark.Client:
    for k, v in config.lark.__dict__.items():
        if k == client_type.value:
            return (
                lark.Client.builder()
                .app_id(v.app_id)
                .app_secret(v.app_secret)
                .timeout(30)
                .build()
            )
    raise ValueError(f"不支持的Lark客户端类型: {client_type}")
    
