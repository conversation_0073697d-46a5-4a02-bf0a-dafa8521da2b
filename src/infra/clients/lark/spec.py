import lark_oapi as lark
from abc import ABCMeta, abstractmethod

from lark_oapi.event.callback.model.p2_card_action_trigger import P2CardActionTrigger


class MessageHandleSpec(metaclass=ABCMeta):
    @abstractmethod
    async def aon_message(self, data: lark.im.v1.P2ImMessageReceiveV1) -> None:
        pass

    @abstractmethod
    async def aon_action_event(self, data: P2CardActionTrigger) -> None:
        pass

    @abstractmethod
    async def aon_custom_event(self, data: lark.CustomizedEvent) -> None:
        pass
 