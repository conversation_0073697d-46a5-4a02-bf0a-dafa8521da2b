from typing import Optional

import lark_oapi as lark
from lark_oapi import LogLevel
from lark_oapi.event.callback.model.p2_card_action_trigger import (
    P2CardActionTrigger,
    P2CardActionTriggerResponse,
)
from loguru import logger

from config.globalconfig import get_or_create_settings_ins
from src.infra.clients.lark.spec import MessageHandleSpec


class LarkMessageReceiver:

    def __init__(self, message_handler: MessageHandleSpec):
        self._lark_client: Optional[lark.ws.client.Client] = None
        self._config = get_or_create_settings_ins()
        # 初始化消息处理组件
        self.handler: message_handler = None

        self._init_lark_client()

    def _init_lark_client(self):

        self._lark_client = lark.ws.Client(
            self._config.lark.trading.app_id,
            self._config.lark.trading.app_secret,
            event_handler=self._build_event_handler(),
            log_level=LogLevel.WARNING,
            auto_reconnect=True,
        )

    def _build_event_handler(self):
        """构建事件处理器"""

        def do_p2_im_message_receive_v1(data: lark.im.v1.P2ImMessageReceiveV1) -> None:
            lark.ws.client.loop.create_task(self.handler.aon_message(data.event))

        def do_card_action_trigger(
            data: P2CardActionTrigger,
        ) -> P2CardActionTriggerResponse:
            lark.ws.client.loop.create_task(self.handler.aon_action_event(data.event))
            return P2CardActionTriggerResponse(None)

        return (
            lark.EventDispatcherHandler.builder("", "")
            .register_p2_im_message_receive_v1(do_p2_im_message_receive_v1)
            .register_p2_card_action_trigger(do_card_action_trigger)
            .build()
        )

    def start(self) -> None:
        """启动接收器"""
        try:
            logger.info("Starting communicate client...")
            self._lark_client.start()
        except Exception as e:
            raise e

    def stop(self) -> None:
        """停止接收器"""
        try:
            if lark.ws.client.loop and lark.ws.client.loop.is_running():
                lark.ws.client.loop.call_soon_threadsafe(
                    lambda: lark.ws.client.loop.stop()
                )
                logger.info("Lark client event loop stopped")
        except Exception as e:
            logger.warning(f"Error stopping communicate client: {e}")
