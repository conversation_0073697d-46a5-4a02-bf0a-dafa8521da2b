import json
import typing
from datetime import datetime

from loguru import logger
from pymysql.err import OperationalError
from sqlalchemy import (
    Column,
    create_engine,
    Engine,
    text,
    DateTime,
    Date,
)
from sqlalchemy.dialects.mysql import BIGINT, insert
from sqlalchemy.orm import DeclarativeBase
from sqlalchemy.orm import sessionmaker, Session

from config.globalconfig import GlobalConfig

TablePrefix = "guanfu"

__all__ = [
    "init_db_engine",
    "init_db_session",
    "SessionLocal",
    "BaseModel",
    "IdModelMixin",
    "DateTimeModelMixin",
    "DateModelMixin",
    "DBSessionContext",
    "bulk_insert",
    "bulk_update",
    "bulk_update_v2",
    "TablePrefix",
]


def init_db_engine(config: GlobalConfig) -> typing.Optional[Engine]:
    """初始化数据库引擎，为1核1G MySQL服务器优化连接池配置"""
    link = f"mysql+pymysql://{config.mysql.user}:{config.mysql.password}@{config.mysql.host}:{config.mysql.port}/{config.mysql.db}?charset={config.mysql.charset}"

    # 使用配置文件中的连接池参数
    engine = create_engine(
        link,
        # 核心连接池参数
        pool_size=config.mysql.pool_size,  # 基础连接数
        max_overflow=config.mysql.max_overflow,  # 允许的额外连接数
        pool_recycle=config.mysql.pool_recycle,  # 连接回收时间(秒)
        pool_timeout=config.mysql.pool_timeout,  # 获取连接的超时时间
        pool_pre_ping=config.mysql.pool_pre_ping,  # 使用前检查连接有效性
        # 连接参数
        connect_args={"connect_timeout": config.mysql.conn_timeout},
        echo=config.mysql.debug,
    )

    try:
        with engine.connect() as conn:
            result = conn.execute(text("SELECT version();"))
            db_version = result.fetchone()

            # 记录连接池配置信息
            pool_info = f"pool_size={config.mysql.pool_size}, max_overflow={config.mysql.max_overflow}"
            logger.info(f"MySQL连接成功. 版本: {db_version}, 连接池配置: {pool_info}")

        return engine
    except Exception as e:
        logger.exception(f"MySQL连接失败: {e}")
        return None


SessionLocal: sessionmaker


def init_db_session(engine: Engine):
    global SessionLocal
    SessionLocal = sessionmaker(bind=engine, autoflush=True)


class BaseModel(DeclarativeBase):
    __allow_unmapped__ = True

    def to_dict(self) -> typing.Dict[str, typing.Any]:
        res = {}
        for c in self.__table__.columns:
            res[c.name] = getattr(self, c.name)
        return res

    def to_json(self) -> str:
        res = {}
        for c in self.__table__.columns:
            value = getattr(self, c.name)
            if isinstance(value, datetime):
                value = value.timestamp() * 1000
            res[c.name] = value
        return json.dumps(res)


class IdModelMixin(BaseModel):
    __abstract__ = True
    id = Column(BIGINT(unsigned=True), primary_key=True, autoincrement=True)


class DateTimeModelMixin(BaseModel):
    __abstract__ = True
    created_at = Column(
        DateTime, default=datetime.now, nullable=False, comment="创建日期"
    )
    updated_at = Column(
        DateTime,
        default=datetime.now,
        onupdate=datetime.now,
        nullable=False,
        comment="更新日期",
    )


class DateModelMixin(BaseModel):
    __abstract__ = True
    created_at = Column(Date, default=datetime.now, nullable=False, comment="创建日期")
    updated_at = Column(
        Date,
        default=datetime.now,
        onupdate=datetime.now,
        nullable=False,
        comment="更新日期",
    )


class DBSessionContext:
    def __init__(self):
        self.sess = None
        try:
            self.sess = SessionLocal()
        except OperationalError as e:
            logger.exception(f"DB会话初始化失败: {e}")
            # 处理常见的连接错误
            if "MySQL server has gone away" in str(e) or "Broken pipe" in str(e):
                logger.info("尝试重新创建会话...")
                self.sess = SessionLocal()

    def __enter__(self) -> Session:
        return self.sess

    def __exit__(self, exc_type, exc_value, traceback):
        if self.sess:
            self.sess.close()





def bulk_insert(session: Session, model, objects: list, return_defaults=False):
    """
    批量插入数据到指定模型表中

    Args:
        session: SQLAlchemy会话
        model: 模型类
        objects: 模型实例列表
        return_defaults: 是否返回自动生成的默认值（如自增ID）

    Returns:
        None
    """
    if not objects:
        return

    # 准备数据字典列表
    data_dicts = []
    for obj in objects:
        # 获取对象的所有属性
        data_dict = {}
        for column in model.__table__.columns:
            # 跳过主键列（如果是自增ID）
            if column.primary_key and column.autoincrement:
                continue

            # 获取属性值
            value = getattr(obj, column.name, None)

            # 处理可能为空但不允许为NULL的列
            if value is None and not column.nullable:
                # 对于日期时间列，使用当前时间
                if isinstance(column.type, (DateTime, Date)):
                    value = datetime.now()

            data_dict[column.name] = value

        data_dicts.append(data_dict)

    # 执行批量插入
    session.execute(model.__table__.insert(), data_dicts)


def bulk_update(session: Session, model, objects: list):
    """
    批量更新数据到指定模型表中

    此方法适用于对象数量较少或者每个对象的更新列不同的情况。
    如果有大量对象需要更新相同的列，请使用 bulk_update_v2 方法。

    示例：
    ```python
    # 更新多个不同类型的对象
    with app.orm_session() as session:
        # 获取需要更新的对象
        stocks = session.query(dao.ZhStock).filter(dao.ZhStock.code.in_(["000001", "600000"])).all()

        # 对每个对象进行不同的更新
        for stock in stocks:
            if stock.code == "000001":
                stock.newest_price = 10.5
                stock.turnover = 2.3
            elif stock.code == "600000":
                stock.price_change = 0.5
                stock.volume = 1000000

        # 使用批量更新
        bulk_update(session, dao.ZhStock, stocks)
        session.commit()
    ```

    Args:
        session: SQLAlchemy会话
        model: 模型类
        objects: 模型实例列表

    Returns:
        None
    """
    if not objects:
        return

    # 获取主键列名
    primary_key_columns = [c.name for c in model.__table__.primary_key.columns]
    if not primary_key_columns:
        logger.error(f"模型 {model.__name__} 没有主键，无法执行批量更新")
        return

    # 按批次处理更新，避免一次性处理过多对象
    batch_size = 100
    for i in range(0, len(objects), batch_size):
        batch = objects[i:i+batch_size]

        # 对每个对象生成更新操作
        for obj in batch:
            # 构建主键条件
            primary_key_values = {}
            for pk_col in primary_key_columns:
                primary_key_values[pk_col] = getattr(obj, pk_col)

            # 构建要更新的值
            update_values = {}
            for column in model.__table__.columns:
                # 跳过主键列
                if column.name in primary_key_columns:
                    continue

                # 获取属性值
                value = getattr(obj, column.name, None)

                # 处理可能为空但不允许为NULL的列
                if value is None and not column.nullable:
                    # 对于日期时间列，使用当前时间
                    if isinstance(column.type, (DateTime, Date)):
                        value = datetime.now()

                update_values[column.name] = value

            # 执行更新
            if update_values:
                session.execute(
                    model.__table__.update()
                    .where(*[getattr(model.__table__.c, pk) == primary_key_values[pk] for pk in primary_key_columns])
                    .values(**update_values)
                )

    # 注意：这里不提交事务，由调用者决定何时提交


def bulk_update_v2(session: Session, model, objects: list):
    """
    批量更新数据到指定模型表中（优化版）

    当有大量对象需要更新相同列时，此方法性能更高。
    此方法使用 MySQL 的 INSERT ... ON DUPLICATE KEY UPDATE 语法，
    可以在一次数据库访问中更新多个记录，显著减少网络往返和数据库负载。

    使用场景：
    1. 批量更新大量相似对象（如股票行情数据）
    2. 需要高性能的批量更新操作
    3. 对象的更新列相对一致

    示例：
    ```python
    # 更新多只股票的行情数据
    with app.orm_session() as session:
        stocks = session.query(dao.ZhStock).filter(dao.ZhStock.is_delisting == False).all()

        # 更新股票数据
        for stock in stocks:
            stock.newest_price = new_price_data[stock.code]
            stock.price_change = price_change_data[stock.code]
            # ... 更新其他字段

        # 使用批量更新
        bulk_update_v2(session, dao.ZhStock, stocks)
        session.commit()
    ```

    Args:
        session: SQLAlchemy会话
        model: 模型类
        objects: 模型实例列表

    Returns:
        None
    """
    if not objects:
        return

    # 获取主键列名
    primary_key_columns = [c.name for c in model.__table__.primary_key.columns]
    if not primary_key_columns:
        logger.error(f"模型 {model.__name__} 没有主键，无法执行批量更新")
        return

    # 获取所有非主键列
    non_pk_columns = [c.name for c in model.__table__.columns if c.name not in primary_key_columns]

    # 按批次处理更新，避免一次性处理过多对象
    batch_size = 100
    for i in range(0, len(objects), batch_size):
        batch = objects[i:i+batch_size]

        # 将对象按照要更新的列分组
        # 这样可以将相同列更新的对象合并到一个查询中
        column_groups = {}

        for obj in batch:
            # 确定要更新的列
            update_columns = []
            for col in non_pk_columns:
                value = getattr(obj, col, None)
                if value is not None or hasattr(obj, col):
                    update_columns.append(col)

            # 将列名列表转换为不可变的元组，以便用作字典键
            columns_key = tuple(sorted(update_columns))

            if columns_key not in column_groups:
                column_groups[columns_key] = []

            column_groups[columns_key].append(obj)

        # 对每组列相同的对象执行批量更新
        for columns_key, group_objects in column_groups.items():
            if not columns_key:  # 跳过没有要更新列的对象
                continue

            # 对每个对象生成一个数据字典
            data_dicts = []
            for obj in group_objects:
                data_dict = {}

                # 添加主键值
                for pk_col in primary_key_columns:
                    data_dict[pk_col] = getattr(obj, pk_col)

                # 添加要更新的列值
                for col in columns_key:
                    value = getattr(obj, col, None)

                    # 处理可能为空但不允许为NULL的列
                    column = model.__table__.columns[col]
                    if value is None and not column.nullable:
                        # 对于日期时间列，使用当前时间
                        if isinstance(column.type, (DateTime, Date)):
                            value = datetime.now()

                    data_dict[col] = value

                data_dicts.append(data_dict)

            # 使用批量更新语句
            if data_dicts:
                # 使用 INSERT ... ON DUPLICATE KEY UPDATE 语法
                # 这在MySQL中是最高效的批量更新方式
                stmt = insert(model.__table__).values(data_dicts)

                # 指定当出现重复键时要更新的列
                on_duplicate_key_stmt = stmt.on_duplicate_key_update(
                    **{col: stmt.inserted[col] for col in columns_key}
                )

                # 执行更新
                session.execute(on_duplicate_key_stmt)

    # 注意：这里不提交事务，由调用者决定何时提交
