from datetime import datetime, timed<PERSON>ta
from typing import Callable, Literal, List

from apscheduler.job import Job
from apscheduler.schedulers.background import BackgroundScheduler
from apscheduler.triggers.cron import CronTrigger
from apscheduler.triggers.date import DateTrigger
from apscheduler.triggers.interval import IntervalTrigger
from loguru import logger

from config.globalconfig import get_or_create_settings_ins

# trigger 的参数参考
_ = CronTrigger
_ = DateTrigger
_ = IntervalTrigger


class AppScheduler:
    INTERVAL = "interval"
    CRON = "cron"
    DATE = "date"

    def __init__(self):
        self.config = get_or_create_settings_ins()
        self._scheduler: BackgroundScheduler = self._init_scheduler()

    def _init_scheduler(self) -> BackgroundScheduler:
        # 使用MySQL作为作业存储
        mysql_url = (
            f"mysql+pymysql://"
            f"{self.config.mysql.user}:{self.config.mysql.password}"
            f"@{self.config.mysql.host}:{self.config.mysql.port}/{self.config.mysql.db}"
        )

        # 为1核1G MySQL服务器优化连接池配置
        config = {
            "apscheduler.jobstores.default": {"type": "sqlalchemy", "url": mysql_url},
            "apscheduler.executors.default": {
                "class": "apscheduler.executors.pool:ThreadPoolExecutor",
                "max_workers": "16",  # 减小线程池大小以避免过载MySQL
            },
            "apscheduler.timezone": "Asia/Shanghai",
        }
        return BackgroundScheduler(config)

    @classmethod
    def job_id(cls, func_name: str) -> str:
        return f"job@{func_name}"

    def add_job(
        self,
        func: Callable,
        trigger: Literal["interval", "cron", "date"],
        id=None,
        replace_existing=True,
        **kwargs_,
    ):
        if id is None:
            id = self.job_id(func.__name__)
        self._scheduler.add_job(
            func, trigger, id=id, replace_existing=replace_existing, **kwargs_
        )

    def modify_job(self, id: str, **kwargs_):
        self._scheduler.modify_job(id, **kwargs_)

    def remove_job(self, id: str):
        self._scheduler.remove_job(id)

    def get_jobs(self) -> List[Job]:
        return self._scheduler.get_jobs()

    def pause(self):
        self._scheduler.pause()

    def resume(self):
        self._scheduler.resume()

    def start(self):
        # 暂停状态下启动调度程序，即没有第一次唤醒调用
        self._scheduler.start()

    def stop(self):
        self._scheduler.shutdown()


def ok():
    logger.info("Scheduler is ready for work...")


app_scheduler = AppScheduler()  # run once
app_scheduler.add_job(
    ok,
    "date",
    run_date=datetime.now() + timedelta(seconds=10),
    id=app_scheduler.job_id("ok"),
)
