#!/usr/bin/env python3
"""
操作类型枚举
"""

from enum import Enum


class ActionType(str, Enum):
    """设备操作类型枚举 - 只保留prompt中使用的动作名称"""
    CLICK = "click"
    LONG_PRESS = "long_press"
    TYPE = "type"
    DELETE = "delete"
    INPUT = "input"
    SWIPE = "swipe"
    DRAG = "drag"
    SCROLL = "scroll"
    BACK = "back"
    WAIT = "wait"
    ENTER = "enter"
    OUTPUT = "output"
    FINISHED = "finished"
    FAILED = "failed"
    ERROR = "error"


class SwipeDirection(str, Enum):
    """滑动方向枚举"""
    UP = "up"
    DOWN = "down"
    LEFT = "left"
    RIGHT = "right"


class ExecutionStatus(str, Enum):
    """执行状态枚举"""
    PROCESSING = "processing"  # 执行中，创建默认状态
    SUCCEED = "succeed"        # 成功
    FAILED = "failed"          # 执行失败或程序异常
    TERMINATE = "terminate"    # 终止，用户主动停止任务
    ERROR = "error"            # 错误，任务执行偏离正常测试路径，主动退出


class ActionStatus(str, Enum):
    """动作状态枚举 - 用于 UITaskAction"""
    RUNNING = "running"        # 执行中
    COMPLETED = "completed"  # 完成
    FAILED = "failed"          # 失败
    TERMINATE = "terminate"  # 终止，用户主动停止任务
