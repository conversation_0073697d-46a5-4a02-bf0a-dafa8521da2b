from src.infra.app import app
from src.infra.web.api import sys_router
from src.api.v1 import task_router, reference_task_router


API_V1 = "/api/v1"

app.http_server.register_router(router=sys_router, tags=["system"])

app.http_server.register_router(
    router=task_router, tags=["ui-task"], prefix=API_V1
)

app.http_server.register_router(
    router=reference_task_router, tags=["reference-task"], prefix=API_V1
)