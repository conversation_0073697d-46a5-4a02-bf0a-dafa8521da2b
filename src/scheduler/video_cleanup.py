#!/usr/bin/env python3
"""
视频清理定时任务
"""

from loguru import logger
from src.domain.ui_task.mobile.service.video_service import video_service
from src.infra.scheduler import app_scheduler


def cleanup_old_videos():
    """
    清理2天前的视频文件
    每天凌晨2点执行
    """
    try:
        logger.info("🧹 Starting scheduled video cleanup task...")
        
        # 清理2天前的视频
        deleted_count = video_service.cleanup_old_videos(days_to_keep=2)
        
        if deleted_count > 0:
            logger.info(f"✅ Video cleanup completed successfully, deleted {deleted_count} old videos")
        else:
            logger.info("✅ Video cleanup completed, no old videos to delete")
            
    except Exception as e:
        logger.error(f"❌ Video cleanup task failed: {str(e)}")
        import traceback
        traceback.print_exc()


# 添加定时任务：每天凌晨2点执行视频清理
app_scheduler.add_job(
    cleanup_old_videos,
    "cron",
    hour=2,
    minute=0,
    id=app_scheduler.job_id("cleanup_old_videos"),
    replace_existing=True
)

logger.info("📅 Video cleanup scheduled task registered: daily at 2:00 AM")
