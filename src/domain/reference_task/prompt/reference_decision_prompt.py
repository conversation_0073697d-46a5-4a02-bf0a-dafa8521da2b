#!/usr/bin/env python3
"""
参考任务决策Prompt

基于参考成功案例的UI自动化测试决策prompt
参考decision_definition_prompt.py的编写风格和描述方式

Prompt块引用说明：
- get_reference_role_definition(): 角色定位 - 定义Agent的核心职责
- get_reference_test_case_description(): 测试用例信息 - 提供当前测试用例的详细信息
- _build_reference_actions_text(): 成功案例 - 提供参考的成功执行历史
- _build_current_execution_history(): 执行记忆 - 当前任务的执行记录
- get_image_element_extractor_prompt(): 图片元素提取器 - 指导如何从截图中提取相关元素
- get_interface_analysis_prompt(): 界面分析 - 指导如何分析当前界面
- get_reference_action_list(): 动作列表 - 可执行的动作类型和格式
- get_reference_learning_strategy(): 执行策略 - 如何学习和复用成功案例
- get_reference_exception_handling(): 特殊场景 - 处理各种异常情况的策略
- get_reference_action_decision_prompt(): 动作决策 - 决策流程和步骤
- get_reference_output_example(): 输出格式 - JSON输出格式要求
- get_reference_output_requirement(): 输出要求 - 输出的具体要求
- get_reference_execution_invoke_prompt(): 执行流程 - 整体执行流程指导
- get_reference_user_invoke_prompt(): 用户调用提示 - 最终的执行和输出要求
"""
import json
import re

from loguru import logger

from src.domain.ui_task.mobile.aggregate.prompt.decision_definition_prompt import verify_expected_result
from src.domain.ui_task.mobile.repo.do.State import DeploymentState


def build_three_step_reference_decision_prompt(state: DeploymentState, current_step_description: str,
                                               enable_thinking: bool = False) -> str:
    """
    构建三步骤参考任务决策prompt，包含上一个、当前、下一个步骤的信息

    Args:
        state: 参考任务状态
        current_step_description: 当前步骤描述
        enable_thinking: 用于输出格式的thinking参数，如果为None则使用原始逻辑

    Returns:
        完整的系统prompt和原始thinking状态（用于兼容性）
    """
    # 获取步骤信息
    current_step_index = state.get("current_step_index", 0)
    task_steps = state.get("task_steps", [])

    # 构建三步骤测试用例信息
    three_step_test_case_info, current_step_name, next_step_name = get_three_step_test_case_description(state,
                                                                                                        current_step_index,
                                                                                                        task_steps)
    # 构建前一个步骤和当前步骤的执行记忆
    combined_execution_history = _build_combined_execution_history(
        state.get("history", [])
    )
    # 构建成功案例：按执行轮次顺序显示，包含当前步骤和下一步骤的成功案例
    combined_reference_text = _build_combined_reference_actions_text(
        state.get("reference_actions", []),
        current_step_description,
        task_steps[current_step_index + 1] if current_step_index + 1 < len(task_steps) else None,
        task_steps,
        current_step_index
    )

    prompt = f"""
########## 角色定位 ##########
{get_three_step_reference_role_definition()}

########## 测试用例信息 ##########
{three_step_test_case_info}

########### 预期结果验证 ##########
{verify_expected_result(state)}

########## 成功案例 ##########
**成功案例动态加载当前执行步骤全部执行记录以及下一步骤第一轮执行记录，当你切换到下一执行步骤时，成功案例会自动切换到下一步骤的第一轮执行记录**
{combined_reference_text}

########## 执行记忆 ##########
{combined_execution_history}

########## 界面分析 ##########
{get_interface_analysis_prompt()}

########## 动作列表 ##########
{get_reference_action_list()}

########## 特殊场景 ##########
{get_reference_exception_handling()}
{get_dynamic_content_comparison_module(state)}

########## 动作决策 ##########
{get_three_step_reference_action_decision_prompt(current_step_name, next_step_name)}

########## 输出格式 ##########
{get_three_step_output_example_with_thinking_param(enable_thinking)}

########## 输出要求 ##########
{get_reference_output_requirement()}
"""
    print(prompt)
    return prompt


def get_three_step_reference_role_definition() -> str:
    """获取三步骤参考任务的角色定义"""
    return """你是一个资深安卓UI回归测试专家，请参照<成功案例>，重新执行一遍所有用例步骤，完成回归测试"""


def get_three_step_reference_action_decision_prompt(current_step_name: str, next_step_name: str = None) -> str:
    """获取三步骤参考动作决策提示，包含当前步骤和下一步骤信息"""

    return f"""**严格按照顺序执行动作决策**
1. **分析当前界面**
   - 调用<界面分析>对<当前轮截图>进行详细分析
   - 如果界面包含<特殊场景>：参照<特殊场景>进行处理

2. **测试路径偏离检测与纠正**
   - 如果<当前轮截图>与<执行记忆><成功案例>没有关联，说明脱离了测试路径
   - 将<当前轮截图>、<执行记忆>、<成功案例>结合分析，制定返回正确测试路径的方案

3. **记录执行进度**
   - <执行记忆>是你本次执行的记录，读取<执行记忆>时刻记录着回归测试执行进度，继续推进回归测试
   - 确保按照测试用例顺序执行，不允许重复执行已经完成的步骤；不允许跳过未执行过的步骤
   - **回归测试重要提醒**：即使看到界面已有预期内容，也不能认为步骤已完成，必须重新执行操作来验证功能

4. **切换执行步骤**
   * 当前正在执行步骤: {current_step_name}，下一执行步骤: {next_step_name}
   * 分析<执行记忆><当前轮截图>:
     - 如果当前步骤没有执行，则参照<成功案例>执行当前步骤： current_step_name={current_step_name}
     - 如果当前步骤已经执行，结合<执行记忆><成功案例>分析,判断步骤{current_step_name}的<执行记忆>执行轮数和决策与<成功案例>是否一致
       - 如果步骤未完成，则继续按照<成功案例>执行
       - 如果步骤已经完成，则切换到下一执行步骤，current_step_name={next_step_name}
   * 切换步骤后一定要将current_step_name更新，推进回归测试执行
   * 步骤执行状态判断流程：
     - 检查<执行记忆>中是否有当前步骤的操作记录
     - 如果没有操作记录 → 步骤未执行 → 必须执行操作
     - 如果有操作记录 → 检查操作是否完整 → 不完整继续执行，完整则切换下一步

5. **参考成功案例**
   * 获取最新current_step_name, current_step_name不需要操作界面(例如：等待、制定方案、理解等非操作界面动作)，则本次决策直接复用<成功案例>的'决策内容'、'操作指令'、'执行动作'
   * 获取最新current_step_name, 从<成功案例>中找到当前步骤的执行记录，成功案例包含了决策内容、决策动作，参考文本内容描述的元素，制定下一步执行动作
   **注意事项**
     - 输入内容时，是否需要删除原有内容，要与<成功案例>动作保持一致，如果<成功案例>直接输入内容，则本次输入前不需要删除原有内容，也直接输入内容
     - 使用output输出/描述内容时，要以<当前轮截图>为准,因为界面内容可能是不固定的，存在与<成功案例>输出/描述内容不同的情况，尤其是修改类型的测试用例

6. **用例结束判断**
   - **步骤完成判断**：当前步骤的目标已达成时，如果还有下一执行步骤，则继续执行下一个步骤，不要调用finished
   - **任务完成判断**：分析<执行记忆>只有当<测试用例信息>中的所有步骤都已执行完毕时，调用<预期结果验证>输出最终结果
   - **重要提醒**：finished()只能在执行完所有步骤后调用，不能在中间步骤调用
   - **终止条件**：分析<执行记忆>最近5轮执行记录,出现以下任一情况立即调用error
     * 路径偏离：连续5轮无法返回正确测试路径
     * 进度停滞：连续5轮界面分析结果完全相同
     * 重复操作：连续5次执行相同动作且界面无变化
     * 异常循环：连续5次遇到相同异常且无法解决
   - **继续执行**：未达到上述任一终止条件，则继续执行"""


def get_three_step_test_case_description(state: DeploymentState, current_step_index: int, task_steps: list):
    """获取三步骤测试用例描述 - 显示全部步骤，标记当前执行的步骤，并返回下一步骤名称"""
    # 优先使用实际的测试用例名称，避免显示参考任务ID
    test_case_name = state.get("test_case_name", "未知测试用例")

    # 构建步骤信息，显示全部步骤
    step_info = ""
    current_step_name = ""
    next_step_name = None

    for i, step in enumerate(task_steps):
        if i == current_step_index:
            current_step_name = step
            step_info += f"**{step}(当前执行步骤)**\n"
            # 获取下一步骤名称
            if i + 1 < len(task_steps):
                next_step_name = task_steps[i + 1]
        else:
            # 其他步骤正常显示
            step_info += f"{step}\n"

    content = f"""- **用例名称**: {test_case_name}
- **用例步骤**:
{step_info}"""

    return content, current_step_name, next_step_name


def get_interface_analysis_prompt() -> str:
    """获取界面分析提示"""
    return """结合<当前轮截图>和<测试用例信息> 的'用例步骤'理解界面中图标、UI组件、文字内容和各元素的特征和位置信息
- 在界面中定位与<测试用例信息>的'用例步骤'的相关元素，必须严格按照'用例步骤'描述的**元素特征**和**位置信息**
- 优先根据 <测试用例信息>的'用例步骤'中描写的方位去聚焦并定位元素，要确定元素特征和位置的唯一性
- 禁止伪造、猜测不存在的元素和内容。"""


def get_reference_action_list() -> str:
    """获取参考任务的动作列表"""
    return """**必须补充动作参数，坐标必须使用标签<point>x1 y1</point>进行包裹**
1.click(point=<point>x1 y1</point>) # 点击屏幕
  - 必须补充点击位置坐标
  - 输入内容前，点击输入框呼出输入法，要点击输入框右侧，不要点击文字内容
2.long_press(point=<point>x1 y1</point>) # 长按屏幕
  - 必须补充长按位置坐标
3.type(content='text_to_input') # 输入文字内容
4.scroll(point='<point>x1 y1</point>', direction='down or up or right or left') 
  - 滑动屏幕，必须补充滑动起点坐标，和direction方向参数
  - 滑动的方向direction就是屏幕被滑动位置移动的方向，向左滑动，页面元素向左移动
  - 由于屏幕是有连续加载性的，当我们向左滑动时，就会从屏幕右侧看到新的内容
5.drag(start_point='<point>x1 y1</point>', end_point='<point>x2 y2</point>') # 拖动元素，必须补充拖动起始坐标和结束坐标
6.wait(seconds=wait_seconds) # 等待，必须补充等待秒数
7.delete(content=delete_count) # 删除指定数量的文字
  - 根据字母、文字、数字个数统计，不是字符
8.back() # 返回
  - 用于返回上一级页面，或关闭弹窗
9.enter() # 回车确认
  - 用于搜索框输入内容后直接搜索
10.output(field_name=value)
  - 用于输出用例步骤要求输出/描述的内容,field_name为动态字段名称，value为需要输出的内容，将value内容赋值给field_name
  - 专门用于输出:全局执行路径、方案、计划内容、步骤要求描述的文字、数字、数据、图片内容、元素特征等
  - 例如：描述图片内容before_image_content，output(before_image_content="图片内容描述")、output(after_image_content="图片内容描述")
11.failed(content='预期结果验证失败原因') # 预期结果验证失败，结束任务
12.finished(content='success_message') # 用例执行完毕，预期结果验证成功，结束任务
13.error(content='主动退出测试用例执行原因') # 偏离测试路径，主动退出测试用例执行"""


def get_three_step_output_example_with_thinking_param(enable_thinking: bool = False) -> str:
    """获取三步骤模式的输出格式要求，支持thinking参数"""
    return f"""**严格按照以下JSON格式输出内容，禁止偷懒，严格按照要求输出每个字段内容**
{{
"interface_analysis": "简单描述<当前轮截图>的<界面分析>结果",
"action_decision": "{'详细' if enable_thinking == 'enabled' else '简单'}描述<动作决策>结果，必须包含：1.说明用例进度以及下一步要做的内容 2.说明是否需要切换步骤 3.说明怎么参考成功案例的内容和执行动作 4.如果遇到需要滑动的步骤，必须描述判断滑动到尽头的过程",
"current_step_name": "当前执行步骤，必须从<测试用例信息>获取步骤序号和描述，必须保证内容的准确性和一致性，不能添加任何其他内容；输出格式：1.点击登录按钮、2.输入用户名等",
"action": "纯净的动作名称和参数,必须引用<动作列表>中的动作，禁止编造<动作列表>中不存在的动作，输出格式：click(point=<point>x1 y1</point>)"
}}
"""


def get_reference_output_requirement() -> str:
    """获取输出要求"""
    return """严格遵循<输出格式>输出规定字段和对应内容，并保证使用JSON格式输出内容
输出的内容必须是JSON格式，方便后续动作及参数的解析和执行
    """


def system_invoke_prompt() -> str:
    return """############ 执行流程 ##########
1.你需要先进行<界面分析>理解<当前轮截图>元素和内容;
2.结合<界面分析>结果，严格遵循<动作决策>环节执行，得出动作决策"结果";
3.将动作决策结果，严格遵守<输出要求>输出最终结果，结果必须符合<输出格式>;
4.当完成<测试用例信息>的全部步骤后，执行<预期结果验证>输出最终结果
"""


def _parse_thought_content(thought_content: str) -> tuple:
    """
    从thought字段解析出五个字段

    Args:
        thought_content: thought字段内容，格式如：
        "界面分析: 界面顶部显示首页横幅...\n执行决策: 根据用例第一步...\n操作指令: 点击页面底部导航栏...\n执行动作: click"

    Returns:
        (界面分析, 步骤名称, 执行决策, 操作指令, 执行动作) 的元组
    """
    if not thought_content:
        return "", "", "", "", ""

    interface_analysis = ""
    current_step_name = ""
    action_decision = ""
    operation_instruction = ""
    action = ""

    # 按行分割内容
    lines = thought_content.split('\n')

    for line in lines:
        line = line.strip()
        if line.startswith("界面分析:") or line.startswith("界面分析："):
            interface_analysis = re.sub(r'^界面分析[:：]\s*', '', line)
        elif line.startswith("执行决策:") or line.startswith("执行决策："):
            action_decision = re.sub(r'^执行决策[:：]\s*', '', line)
        elif line.startswith("操作指令:") or line.startswith("操作指令："):
            operation_instruction = re.sub(r'^操作指令[:：]\s*', '', line)
        elif line.startswith("执行动作:") or line.startswith("执行动作："):
            action = re.sub(r'^执行动作[:：]\s*', '', line)

    return interface_analysis, current_step_name, action_decision, operation_instruction, action


def _parse_decision_content_new(decision_content: str) -> tuple:
    """
    从decision_content解析出五个字段（新格式）

    Args:
        decision_content: 决策内容字符串

    Returns:
        (界面分析, 步骤名称, 执行决策, 操作指令, 执行动作) 的元组
    """
    if not decision_content:
        return "", "", "", ""

    interface_analysis = ""
    current_step_name = ""
    action_decision = ""
    operation_instruction = ""
    action = ""

    # 尝试解析JSON格式
    try:
        if decision_content.strip().startswith('{'):
            data = json.loads(decision_content)
            interface_analysis = data.get("interface_analysis", "")
            current_step_name = data.get("current_step_name", "")
            action_decision = data.get("action_decision", "")
            operation_instruction = data.get("instruction", "")
            action = data.get("action", "")
            return interface_analysis, current_step_name, action_decision, operation_instruction, action
    except:
        pass

    # 使用正则表达式解析
    lines = decision_content.split('\n')
    for line in lines:
        line = line.strip()
        if re.match(r'.*界面分析.*[:：]', line):
            interface_analysis = re.sub(r'.*界面分析.*[:：]\s*', '', line)
        elif re.match(r'.*步骤名称.*[:：]', line):
            current_step_name = re.sub(r'.*步骤名称.*[:：]\s*', '', line)
        elif re.match(r'.*执行决策.*[:：]', line):
            action_decision = re.sub(r'.*执行决策.*[:：]\s*', '', line)
        elif re.match(r'.*操作指令.*[:：]', line):
            operation_instruction = re.sub(r'.*操作指令.*[:：]\s*', '', line)
        elif re.match(r'.*执行动作.*[:：]', line):
            action = re.sub(r'.*执行动作.*[:：]\s*', '', line)

    return interface_analysis, current_step_name, action_decision, operation_instruction, action


def _escape_template_variables(text: str) -> str:
    """
    转义文本中的模板变量符号，防止LangChain将其识别为变量

    Args:
        text: 原始文本

    Returns:
        转义后的文本
    """
    if not text:
        return text

    # 将单个花括号转义为双花括号
    # 这样LangChain就不会将其识别为模板变量
    return text.replace("{", "{{").replace("}", "}}")


def _build_combined_reference_actions_text(reference_actions: list, current_step_description: str,
                                           next_step_description: str = None, task_steps: list = None,
                                           current_step_index: int = 0) -> str:
    """
    构建组合的成功案例文本，动态加载当前执行步骤全部执行记录以及下一步骤第一轮执行记录

    Args:
        reference_actions: 参考动作列表
        current_step_description: 当前步骤描述
        next_step_description: 下一步骤描述（可选）
        task_steps: 任务步骤列表，用于匹配步骤名称序号
        current_step_index: 当前步骤索引

    Returns:
        格式化的组合成功案例文本
    """
    if not reference_actions:
        return ""

    # 按步骤序号分组成功案例
    step_groups_dict = {}
    for action in reference_actions:
        step_name = action.get("step_name", "")
        if step_name:
            step_number = _extract_step_index_from_name(step_name)
            if step_number is not None:
                if step_number not in step_groups_dict:
                    step_groups_dict[step_number] = []
                step_groups_dict[step_number].append(action)

    if not step_groups_dict:
        return "暂无相关成功案例"

    current_step_number = current_step_index + 1  # 转换为步骤序号（从1开始）
    next_step_number = current_step_number + 1

    # 构建成功案例文本：当前步骤全部执行记录 + 下一步骤第一轮执行记录
    actions_text = ""
    execution_count = 1

    # 1. 添加当前步骤的所有执行记录
    if current_step_number in step_groups_dict:
        current_step_actions = step_groups_dict[current_step_number]
        for action in current_step_actions:
            formatted_action = _format_execution_record_with_step_name(action, execution_count, task_steps)
            if formatted_action:
                actions_text += formatted_action
                execution_count += 1

    # 2. 添加下一步骤的第一轮执行记录
    if next_step_number in step_groups_dict:
        next_step_actions = step_groups_dict[next_step_number]
        if next_step_actions:  # 只取第一轮执行记录
            first_next_action = next_step_actions[0]
            formatted_action = _format_execution_record_with_step_name(first_next_action, execution_count, task_steps)
            if formatted_action:
                actions_text += formatted_action

    return actions_text


def _group_actions_by_step_order(actions: list) -> list:
    """
    将动作按步骤序号分组
    现在action表中存储了完整的步骤名称（包含序号），可以按序号精确分组

    Args:
        actions: 所有动作记录列表（字典格式）

    Returns:
        按步骤序号分组的动作列表，每个元素是一个步骤的所有动作
    """
    try:
        import re

        # 使用字典按步骤序号分组
        step_groups_dict = {}

        for action in actions:
            step_name = action.get("step_name", "")
            if not step_name:
                continue

            # 提取步骤序号
            step_index = _extract_step_index_from_name(step_name)
            if step_index is not None:
                if step_index not in step_groups_dict:
                    step_groups_dict[step_index] = []
                step_groups_dict[step_index].append(action)

        # 按序号排序并转换为列表
        sorted_step_indices = sorted(step_groups_dict.keys())
        step_groups = []

        for step_index in sorted_step_indices:
            step_groups.append(step_groups_dict[step_index])

        return step_groups

    except Exception as e:
        # 如果出错，返回空列表
        return []


def _extract_step_index_from_name(step_name: str) -> int | None:
    """
    从步骤名称中提取步骤序号

    Args:
        step_name: 步骤名称，如 "1.点击底部消息tab" 或 "2.点击页面右下角的"聊天"按钮"

    Returns:
        步骤序号（从1开始），如果无法提取则返回None
    """
    if not step_name:
        return None

    try:
        import re
        # 匹配步骤序号的正则表达式，支持多种格式
        # 匹配格式：数字. 数字) 数字、 数字- 数字空格 等
        step_pattern = r'^(\d+)[.)、\-\s）]+'
        match = re.match(step_pattern, step_name.strip())

        if match:
            step_index = int(match.group(1))
            return step_index
        else:
            return None

    except (ValueError, AttributeError):
        return None


def _match_step_name_with_task_steps(step_name: str, task_steps: list) -> str:
    """
    将步骤名称与task_steps匹配，如果匹配成功就返回带序号的步骤名称

    Args:
        step_name: 原始步骤名称
        task_steps: 任务步骤列表

    Returns:
        匹配后的步骤名称（如果匹配成功则带序号，否则返回原名称）
    """
    if not step_name or not task_steps:
        return step_name

    # 去除原步骤名称中的序号（如果有的话）
    import re
    step_name_clean = re.sub(r'^\d+[.)、\-\s]*', '', step_name.strip())

    # 在task_steps中查找匹配的步骤
    for task_step in task_steps:
        # 去除task_step中的序号
        task_step_clean = re.sub(r'^\d+[.)、\-\s]*', '', task_step.strip())

        # 如果内容匹配，返回task_step（带序号）
        if step_name_clean == task_step_clean:
            return task_step

    # 如果没有匹配，返回原步骤名称
    return step_name


def _format_execution_record_with_step_name(record: dict, execution_count: int, task_steps: list = None) -> str:
    """
    格式化执行记录，包含步骤名称

    Args:
        record: 执行记录字典
        execution_count: 执行轮次
        task_steps: 任务步骤列表，用于匹配步骤名称序号

    Returns:
        格式化的执行记录文本
    """
    # 获取步骤名称
    step_name = record.get("step_name", "")

    # 尝试与task_steps匹配，如果匹配成功就使用带序号的步骤名称
    if step_name and task_steps:
        step_name = _match_step_name_with_task_steps(step_name, task_steps)

    # 尝试从parsed_fields获取结构化数据
    parsed_fields = record.get("parsed_fields", {})

    if parsed_fields:
        interface_analysis = parsed_fields.get("interface_analysis", "")
        action_decision = parsed_fields.get("action_decision", "")
        operation_instruction = parsed_fields.get("instruction", "")
        action = parsed_fields.get("action", "")
    else:
        # 从其他字段解析
        decision_content = record.get("decision_content", "")
        thought_content = record.get("thought", "")

        if thought_content:
            interface_analysis, _, action_decision, operation_instruction, action = _parse_thought_content(
                thought_content)
        else:
            interface_analysis, _, action_decision, operation_instruction, action = _parse_decision_content_new(
                decision_content)

    # 如果连步骤名称和动作都没有，则跳过这条记录
    if not step_name and not action:
        return ""

    # 构建格式化文本
    formatted_text = f"""**第{execution_count}轮执行**
- 步骤名称: {step_name}
- 界面分析: {interface_analysis}
- 决策内容: {action_decision}
- 操作指令: {operation_instruction}
- 执行动作: {action}
"""

    return _escape_template_variables(formatted_text)


def _is_step_match(action_step_name: str, current_step_description: str) -> bool:
    """
    判断动作步骤名称是否与当前步骤描述匹配
    使用简单的字符串匹配逻辑

    Args:
        action_step_name: 动作中的步骤名称
        current_step_description: 当前步骤描述

    Returns:
        是否匹配
    """
    if not action_step_name or not current_step_description:
        return False

    # 清理字符串
    action_name = action_step_name.strip()
    current_desc = current_step_description.strip()

    # 1. 完全匹配
    if action_name == current_desc:
        return True

    # 2. 去除序号后完全匹配
    action_name_no_num = re.sub(r'^\d+[.)、\-\s]*', '', action_name)
    current_desc_no_num = re.sub(r'^\d+[.)、\-\s]*', '', current_desc)

    if action_name_no_num == current_desc_no_num:
        return True

    # 3. 标准化后匹配（去除引号、括号等格式差异）
    action_normalized = _normalize_step_text(action_name_no_num)
    current_normalized = _normalize_step_text(current_desc_no_num)

    if action_normalized == current_normalized:
        return True

    return False


def _normalize_step_text(text: str) -> str:
    """
    标准化步骤文本，去除格式差异

    Args:
        text: 原始文本

    Returns:
        标准化后的文本
    """
    if not text:
        return ""

    # 去除各种引号和括号
    normalized = re.sub(r"['\"""（）()]", "", text)
    # 去除斜杠和特殊符号
    normalized = re.sub(r"[/\\]", "", normalized)
    # 去除多余空格
    normalized = re.sub(r'\s+', '', normalized)
    # 转换为小写
    normalized = normalized.lower()

    return normalized


def _build_combined_execution_history(history: list) -> str:
    """
    构建组合执行历史，包含所有执行记录

    Args:
        history: 执行历史列表

    Returns:
        格式化的组合执行历史文本
    """
    if not history:
        return "暂无执行历史"

    # 获取所有step_execution_with_reference的执行记录
    relevant_history = [r for r in history if r.get("action") == "step_execution_with_reference"]

    if not relevant_history:
        return "暂无执行历史"

    # 按时间戳排序
    relevant_history.sort(key=lambda x: x.get("timestamp", ""))

    combined_content = "**以下为你本次回归测试的执行记忆内容**\n"
    execution_round = 0

    # 直接循环追加所有执行记录
    for record in relevant_history:
        ai_response = record.get("ai_response", "")
        action_command = record.get("action_command", "")
        step_description = record.get("step_description", "")

        # 直接解析JSON，因为模型输出都是JSON格式
        try:
            # 清理JSON字符串，移除可能的前后空白和特殊字符
            cleaned_response = ai_response.strip()

            # 如果不是以{开头和}结尾，尝试提取JSON部分
            if not (cleaned_response.startswith('{') and cleaned_response.endswith('}')):
                # 尝试找到JSON部分
                start_idx = cleaned_response.find('{')
                end_idx = cleaned_response.rfind('}')
                if start_idx != -1 and end_idx != -1 and end_idx > start_idx:
                    cleaned_response = cleaned_response[start_idx:end_idx + 1]

            data = json.loads(cleaned_response)
            action_decision = data.get("action_decision", "")
            interface_analysis = data.get("interface_analysis", "")
            # 优先使用AI响应中的current_step_name，如果没有则使用step_description
            current_step_name = data.get("current_step_name", step_description)

            # 直接追加，不做复杂的验证
            execution_round += 1
            formatted_text = f"""**第{execution_round}轮执行**
- 步骤名称: {current_step_name}
- 界面分析: {interface_analysis}
- 决策内容: {action_decision}
- 执行动作: {action_command}
"""
            combined_content += _escape_template_variables(formatted_text)

        except (json.JSONDecodeError, Exception) as e:
            action_decision = ""
            interface_analysis = ""
            current_step_name = step_description  # 默认使用step_description

            # 尝试解析action_decision
            if '"action_decision"' in ai_response:
                try:
                    start = ai_response.find('"action_decision"') + len('"action_decision"')
                    start = ai_response.find('"', start) + 1
                    end = ai_response.find('",', start)
                    if end == -1:
                        end = ai_response.find('"', start)
                    if 0 < start < end:
                        action_decision = ai_response[start:end]
                except:
                    pass

            # 尝试解析interface_analysis
            if '"interface_analysis"' in ai_response:
                try:
                    start = ai_response.find('"interface_analysis"') + len('"interface_analysis"')
                    start = ai_response.find('"', start) + 1
                    end = ai_response.find('",', start)
                    if end == -1:
                        end = ai_response.find('"', start)
                    if 0 < start < end:
                        interface_analysis = ai_response[start:end]
                except:
                    pass

            # 尝试解析current_step_name
            if '"current_step_name"' in ai_response:
                try:
                    start = ai_response.find('"current_step_name"') + len('"current_step_name"')
                    start = ai_response.find('"', start) + 1
                    end = ai_response.find('",', start)
                    if end == -1:
                        end = ai_response.find('"', start)
                    if 0 < start < end:
                        current_step_name = ai_response[start:end]
                except:
                    pass

            execution_round += 1
            formatted_text = f"""**第{execution_round}轮执行**
- 步骤名称: {current_step_name}
- 界面分析: {interface_analysis if interface_analysis else '[解析失败]'}
- 决策内容: {action_decision if action_decision else '[解析失败]'}
- 执行动作: {action_command}
"""
            combined_content += _escape_template_variables(formatted_text)

    return combined_content if execution_round > 0 else "暂无执行历史"


def get_reference_exception_handling() -> str:
    """获取参考任务特殊场景处理策略"""
    return """* 如界面出现弹窗，且弹窗带倒计时，则调用wait(seconds=倒计时秒数)等待倒计时结束自动消失；
* 如界面出现弹窗，且弹窗不带倒计时，但附近存在'我知道了'、'同意'、'取消' 'X'等按钮，则调用click点击按钮关闭弹窗；
* 如界面出现广告，且右上角出现'跳过+数字'按钮，这是带倒计时的广告，例如：跳过1、跳过2、跳过3，必须调用wait(seconds=倒计时数字)等待广告消失；
* 如界面边缘存在悬浮气泡遮挡了目标元素位置，可以先通过拖动(drag)上/下移动悬浮气泡，露出目标元素；
* 如界面出现未加载完成、空白页面、页面切换中、异常页面，则调用wait(seconds=3)等待加载完成，其中若'空白页'和'异常页'出现连续多次(>2次)等待则考虑系统问题；
* 删除2次文字都没有被删除，说明光标位置不对，需要点击文字右侧空白处，将光标移动到末尾，再次删除
* 输入内容时，要先点击输入框呼出输入法，页面底部出现输入法[Switch IME 或 AdbIME]，才能使用type输入内容
**特别注意**：弹窗处理优先级最高，必须确保弹窗处理完毕后，再进行其他操作"""


def get_dynamic_content_comparison_module(state: DeploymentState) -> str:
    """
    构建动态内容对比模块，用于展示运行中保留的内容

    Args:
        state: 当前状态，包含dynamic_fields字段

    Returns:
        格式化的动态内容对比文本
    """
    try:
        dynamic_fields = state.get("dynamic_fields", {})

        if not dynamic_fields:
            return ""

        content_lines = []

        for field_name, field_value in dynamic_fields.items():
            content_lines.append(f"- {field_name}={field_value}")

        return "\n########## 运行中赋值的变量列表 ##########\n**用于保存运行时赋值的数据、图片描述、计划方案等内容，提供给后续步骤使用或预期结果验证**\n" + "\n".join(
            content_lines)

    except Exception as e:
        logger.error(f"Failed to build dynamic content comparison module: {str(e)}")
        return ""
