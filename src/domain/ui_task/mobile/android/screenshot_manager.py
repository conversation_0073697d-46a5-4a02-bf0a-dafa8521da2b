#!/usr/bin/env python3
"""
截图管理工具

负责任务截图的文件系统管理和设备截图操作：
1. 为每个任务创建独立的截图目录
2. 管理截图文件的保存和访问
3. 任务删除时清理截图目录
4. 提供ADB设备操作和截图功能
"""
import base64
import os
import shutil
import traceback
from datetime import datetime
from pathlib import Path

from loguru import logger

from config.globalconfig import get_or_create_settings_ins
from src.domain.ui_task.mobile.service.execution_log_service import ExecutionLogService
from .native_adb_utils import native_adb


class ScreenshotManager:
    """截图管理器"""

    def __init__(self, base_dir: str = None):
        """
        初始化截图管理器

        Args:
            base_dir: 截图根目录，如果为None则从配置文件读取
        """
        if base_dir is None:
            # 从配置文件读取路径
            config = get_or_create_settings_ins()
            base_dir = config.paths.screenshot_base_dir

        self.base_dir = Path(base_dir)
        # 使用 parents=True 确保父目录被创建
        self.base_dir.mkdir(parents=True, exist_ok=True)
    
    def get_task_screenshot_dir(self, task_id: str) -> Path:
        """
        获取任务的截图目录路径

        Args:
            task_id: 任务ID

        Returns:
            任务截图目录路径
        """
        # 创建按日期分组的目录结构
        current_date = datetime.now()
        date_folder = current_date.strftime("%Y%m%d")  # 格式：20250808
        date_dir = self.base_dir / date_folder
        date_dir.mkdir(parents=True, exist_ok=True)

        task_dir = date_dir / task_id
        task_dir.mkdir(exist_ok=True)
        return task_dir

    def get_screenshot_full_path(self, relative_path: str) -> Path:
        """
        根据相对路径获取截图的完整路径
        
        Args:
            relative_path: 截图的相对路径
            
        Returns:
            截图的完整路径
        """
        return self.base_dir / relative_path

    def screenshot_exists(self, relative_path: str) -> bool:
        """
        检查截图文件是否存在
        
        Args:
            relative_path: 截图的相对路径
            
        Returns:
            文件是否存在
        """
        full_path = self.get_screenshot_full_path(relative_path)
        return full_path.exists()

    def delete_task_screenshots(self, task_id: str) -> bool:
        """
        删除任务的所有截图

        Args:
            task_id: 任务ID

        Returns:
            是否删除成功
        """
        try:
            deleted_any = False

            # 遍历所有日期文件夹查找任务截图
            for date_folder in self.base_dir.iterdir():
                if not date_folder.is_dir():
                    continue

                # 检查文件夹名称是否为日期格式（YYYYMMDD）
                folder_name = date_folder.name
                if len(folder_name) != 8 or not folder_name.isdigit():
                    continue

                task_dir = date_folder / task_id
                if task_dir.exists():
                    shutil.rmtree(task_dir)
                    logger.info(f"[task_id: {task_id}] 🗑️ Task screenshots deleted: {task_dir}")
                    deleted_any = True

            if not deleted_any:
                logger.info(f"[task_id: {task_id}] ⚠️ Task screenshot directory not found")

            return True  # 无论是否找到都算成功

        except Exception as e:
            logger.error(f"[task_id: {task_id}] ❌ Failed to delete task screenshots: {str(e)}")
            return False

    def cleanup_old_screenshots(self, days_to_keep: int = 5) -> int:
        """
        清理指定天数前的截图文件

        Args:
            days_to_keep: 保留天数，默认5天

        Returns:
            删除的文件数量
        """
        try:
            from datetime import timedelta

            deleted_count = 0
            current_date = datetime.now()
            cutoff_date = current_date - timedelta(days=days_to_keep)

            logger.info(f"🧹 Starting screenshot cleanup, removing screenshots older than {cutoff_date.strftime('%Y-%m-%d')}")

            # 遍历截图基础目录下的所有日期文件夹
            for date_folder in self.base_dir.iterdir():
                if not date_folder.is_dir():
                    continue

                # 检查文件夹名称是否为日期格式（YYYYMMDD）
                folder_name = date_folder.name
                if len(folder_name) != 8 or not folder_name.isdigit():
                    continue

                try:
                    # 解析文件夹日期
                    folder_date = datetime.strptime(folder_name, "%Y%m%d")

                    # 如果文件夹日期早于截止日期，删除整个文件夹
                    if folder_date < cutoff_date:
                        logger.info(f"🗑️ Deleting old screenshot folder: {date_folder}")

                        # 统计文件数量
                        screenshot_files = []
                        for task_dir in date_folder.iterdir():
                            if task_dir.is_dir():
                                screenshot_files.extend(list(task_dir.glob("*.png")))

                        folder_file_count = len(screenshot_files)

                        # 删除文件夹及其内容
                        shutil.rmtree(date_folder)
                        deleted_count += folder_file_count

                        logger.info(f"✅ Deleted {folder_file_count} screenshots from {folder_name}")

                except ValueError:
                    # 文件夹名称不是有效的日期格式，跳过
                    logger.warning(f"⚠️ Skipping invalid date folder: {folder_name}")
                    continue

            logger.info(f"🎉 Screenshot cleanup completed, deleted {deleted_count} old screenshots")
            return deleted_count

        except Exception as e:
            logger.error(f"❌ Failed to cleanup old screenshots: {str(e)}")
            return 0

    def execute_adb(self, adb_command: str) -> str:
        """
        执行ADB命令（兼容性方法）

        Args:
            adb_command: ADB命令字符串

        Returns:
            命令执行结果或ERROR
        """
        try:
            # 直接使用原生adb执行命令
            success, output = native_adb.execute_adb_command(adb_command, timeout=30)
            if success:
                return output
            else:
                logger.error(f"ADB command failed: {output}")
                return "ERROR"

        except Exception as e:
            logger.error(f"Command exception: {adb_command}, error: {str(e)}")
            # 记录详细的错误信息
            logger.error(f"ADB command traceback: {traceback.format_exc()}")
            return "ERROR"

    def take_screenshot(self,
                        device: str,
                        task_id: str = None,
                        execution_count: int = 0,
                        action_name: str = None
                        ) -> str:
        """
        截取设备屏幕截图并保存到任务目录

        Args:
            device: 设备ID
            task_id: 任务ID
            execution_count: 执行轮数
            action_name: 动作名称

        Returns:
            截图文件的相对路径或错误信息
        """
        try:
            # 获取任务截图目录
            task_dir = self.get_task_screenshot_dir(task_id)

            # 生成文件名
            timestamp = datetime.now().strftime("%Y%m%d_%H%M%S_%f")[:-3]
            if action_name:
                filename = f"{action_name}_{timestamp}.png"
            else:
                filename = f"screenshot_{execution_count}_{timestamp}.png"

            screenshot_file = task_dir / filename

            # 使用原生adb截图
            try:
                # 使用原生adb的screenshot功能
                if native_adb.take_screenshot(device, str(screenshot_file)):
                    # 检查文件是否成功创建
                    if screenshot_file.exists() and screenshot_file.stat().st_size > 0:
                        # 返回相对路径
                        relative_path = str(screenshot_file.relative_to(self.base_dir))
                        logger.info(f"[task_id: {task_id}] 📸 Screenshot saved: {relative_path}")
                        return relative_path
                    else:
                        # 尝试备用方法
                        return self._take_screenshot_fallback(device, str(screenshot_file), task_id)
                else:
                    # 尝试备用方法
                    return self._take_screenshot_fallback(device, str(screenshot_file), task_id)

            except Exception as e:
                logger.error(f"[task_id: {task_id}] Screenshot using native adb failed: {str(e)}")
                # 记录详细的错误信息
                logger.error(f"[task_id: {task_id}] Screenshot traceback: {traceback.format_exc()}")
                # 尝试备用方法
                return self._take_screenshot_fallback(device, str(screenshot_file), task_id)

        except Exception as e:
            logger.error(f"Screenshot failed, error: {str(e)}")
            return f"Screenshot failed, error information: {str(e)}"

    def _take_screenshot_fallback(self, device: str, screenshot_file: str, task_id: str = None) -> str:
        """
        备用截图方法

        Args:
            device: 设备ID
            screenshot_file: 截图文件路径
            task_id: 任务ID

        Returns:
            截图文件路径或错误信息
        """
        try:
            # 备用方法：先保存到设备，再拉取
            remote_file = f"/sdcard/temp_screenshot_{datetime.now().strftime('%H%M%S')}.png"

            # 截图到设备 - 使用native_adb的shell_command方法
            success, output = native_adb.shell_command(device, f"screencap -p {remote_file}")
            if success:
                # 拉取到本地 - 使用native_adb的execute_adb_command方法
                pull_command = f"adb -s {device} pull {remote_file} {screenshot_file}"
                success, output = native_adb.execute_adb_command(pull_command)
                if success:
                    # 删除设备上的临时文件 - 使用native_adb的shell_command方法
                    native_adb.shell_command(device, f"rm {remote_file}")

                    if os.path.exists(screenshot_file) and os.path.getsize(screenshot_file) > 0:
                        # 返回相对路径
                        if task_id:
                            screenshot_path = Path(screenshot_file)
                            relative_path = str(screenshot_path.relative_to(self.base_dir))
                            return relative_path
                        return screenshot_file

            return "Screenshot failed. Please check device connection or permissions."

        except Exception as e:
            # 记录详细的错误信息
            logger.error(f"Fallback screenshot traceback: {traceback.format_exc()}")
            return f"Fallback screenshot failed: {str(e)}"


# 全局截图管理器实例
screenshot_manager = ScreenshotManager()


# 向后兼容的函数，委托给screenshot_manager实例
def execute_adb(adb_command: str) -> str:
    """
    执行ADB命令

    Args:
        adb_command: ADB命令字符串

    Returns:
        命令执行结果或ERROR
    """
    return screenshot_manager.execute_adb(adb_command)


def take_screenshot(
        device: str,
        task_id: str = None,
        execution_count: int = 0,
        action_name: str = None
) -> str:
    """
    截取设备屏幕截图并保存到任务目录

    Args:
        device: 设备ID
        task_id: 任务ID
        execution_count: 执行轮数
        action_name: 动作名称

    Returns:
        截图文件的相对路径或错误信息
    """
    return screenshot_manager.take_screenshot(device, task_id, execution_count, action_name)


def convert_screenshot_to_base64(screenshot_path: str, task_id: str) -> str:
    """
    将截图文件转换为base64格式

    Args:
        screenshot_path: 截图文件路径（可以是相对路径或绝对路径）
        task_id: 任务ID

    Returns:
        base64编码的图片数据，失败返回空字符串
    """
    try:
        # 如果是相对路径，转换为绝对路径
        if not os.path.isabs(screenshot_path):
            full_path = screenshot_manager.get_screenshot_full_path(screenshot_path)
        else:
            full_path = screenshot_path

        with open(full_path, "rb") as f:
            image_content = f.read()
        image_data_base64 = base64.b64encode(image_content).decode("utf-8")
        return image_data_base64
    except Exception as e:
        logger.info(f"[{task_id}] ❌ Failed to convert screenshot to base64: {str(e)}")
        return ""