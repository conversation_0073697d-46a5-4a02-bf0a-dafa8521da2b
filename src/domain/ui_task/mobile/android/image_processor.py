#!/usr/bin/env python3
"""
统一的图像处理模块 - 合并图像标注和图像处理工具功能

本模块整合了原有的两个图像工具模块：
- src.domain.ui_task.mobile.android.image_tools.image_annotator
- src.domain.ui_task.mobile.android.image_tools.image_utils

主要功能：
1. 图像标注：在截图上标注操作坐标和动作位置
2. 图像处理：base64转换、格式转换、大小调整、压缩、裁剪等

向后兼容性：
- image_processor: 新的统一实例（推荐使用）
- image_annotator: 向后兼容的别名，指向同一个实例

使用示例：
    # 新接口
    from src.domain.ui_task.mobile.android.image_processor import image_processor
    result = image_processor.annotate_screenshot_from_action(path, action, task_id)
    
    # 旧接口（向后兼容）
    from src.domain.ui_task.mobile.android.image_processor import image_annotator
    result = image_annotator.annotate_screenshot_from_action(path, action, task_id)
"""

import base64
import os
from pathlib import Path
from typing import Tuple, Optional, Any
from concurrent.futures import ThreadPoolExecutor
from threading import Lock
from datetime import datetime
import queue

from PIL import Image, ImageDraw
from loguru import logger

from src.domain.ui_task.mobile.android.action_tool import parse_action_with_coordinates, ScreenUtils
from src.domain.ui_task.mobile.android.screenshot_manager import screenshot_manager


class ImageProcessor:
    """统一的图像处理器类，整合图像标注和图像处理工具功能"""

    def __init__(self):
        # 图像标注配置 - 更显眼的标记设计
        self.primary_color = (255, 0, 0)
        self.accent_color = (255, 0, 0)  #
        self.base_size = 60  # 基础标记大小
        self.line_width = 6  # 线条宽度

        # 异步处理配置
        self._executor = ThreadPoolExecutor(max_workers=3, thread_name_prefix="ImageProcessor")
        self._annotation_queue = queue.Queue()
        self._is_running = True
        self._lock = Lock()

        # 启动后台处理线程
        self._start_background_processor()

    def _start_background_processor(self):
        """启动后台图像处理线程"""
        def process_annotations():
            """后台处理图像标注任务"""
            while self._is_running:
                try:
                    # 从队列获取标注任务，超时1秒
                    annotation_task = self._annotation_queue.get(timeout=1.0)

                    if annotation_task is None:  # 停止信号
                        break

                    # 执行标注任务
                    self._process_annotation_task(annotation_task)

                except queue.Empty:
                    continue
                except Exception as e:
                    logger.error(f"❌ Error in background annotation processor: {str(e)}")

        # 在线程池中启动后台处理
        self._executor.submit(process_annotations)
        logger.info("🚀 ImageProcessor background annotation processor started")

    def _process_annotation_task(self, task: dict):
        """处理单个图像标注任务"""
        try:
            task_type = task.get('type')

            if task_type == 'annotate_from_action':
                # 处理从动作命令标注的任务
                image_path = task['image_path']
                action_command = task['action_command']
                task_id = task['task_id']
                device = task['device']

                # 执行同步标注
                self._annotate_screenshot_from_action_sync(image_path, action_command, task_id, device)

            elif task_type == 'annotate_with_coordinates':
                # 处理直接坐标标注的任务
                image_path = task['image_path']
                coordinates = task['coordinates']
                direction = task.get('direction')

                # 执行同步标注
                self._annotate_screenshot_with_coordinates_sync(image_path, coordinates, direction)

        except Exception as e:
            task_id = task.get('task_id', 'unknown')
            logger.error(f"[{task_id}] ❌ Error processing annotation task: {str(e)}")

    def _draw_enhanced_marker(self, draw, x: int, y: int, img_width: int, img_height: int, direction: str = None):
        """
        绘制增强的多层标记，让模型更容易识别已操作的位置

        标记设计：
        1. 外圈大圆环（亮绿色）
        2. 中圈圆环（红色）
        3. 内圈小圆环（黄色）
        4. 中心十字准星（白色）
        5. 中心实心圆点（黑色）
        6. 如果有方向信息，绘制方向箭头

        Args:
            draw: PIL绘图对象
            x, y: 标记中心坐标
            img_width, img_height: 图片尺寸
            direction: 滑动方向（可选），用于绘制箭头
        """
        # 根据图片尺寸动态调整标记大小
        scale_factor = min(img_width, img_height) / 1000.0
        scale_factor = max(0.5, min(2.0, scale_factor))  # 限制缩放范围

        # 计算各层标记的半径
        outer_radius = int(self.base_size * scale_factor * 0.8)  # 外圈大圆环
        middle_radius = int(self.base_size * scale_factor * 0.6)  # 中圈圆环
        center_radius = int(self.base_size * scale_factor * 0.1)  # 中心圆点

        # 确保标记不超出图片边界
        max_radius = min(
            x, y,
            img_width - x - 1,
            img_height - y - 1,
            outer_radius
        )
        # 按比例调整所有半径
        if max_radius < outer_radius:
            ratio = max_radius / outer_radius
            outer_radius = int(outer_radius * ratio)
            middle_radius = int(middle_radius * ratio)
            center_radius = max(1, int(center_radius * ratio))

        # 2. 绘制中圈圆环（红色，中等线条）
        draw.ellipse(
            [x - middle_radius, y - middle_radius, x + middle_radius, y + middle_radius],
            outline=self.primary_color,
            width=self.line_width - 2
        )

        # 5. 绘制中心实心圆点（黑色，标记精确点击位置）
        draw.ellipse(
            [x - center_radius, y - center_radius, x + center_radius, y + center_radius],
            fill=self.primary_color
        )

        # 6. 如果有方向信息，绘制方向箭头
        if direction:
            self._draw_direction_arrow(draw, x, y, direction, middle_radius)

        logger.info(f"📍 Enhanced marker drawn at ({x}, {y}) with outer_radius={outer_radius}, direction={direction}")

    def _draw_direction_arrow(self, draw, x: int, y: int, direction: str, base_radius: int):
        """
        绘制小型方向箭头，紧贴圆圈边缘

        Args:
            draw: PIL绘图对象
            x, y: 标记中心坐标
            direction: 滑动方向 ('up', 'down', 'left', 'right')
            base_radius: 基础半径
        """
        # 计算小箭头参数 - 大幅缩小尺寸
        arrow_length = 25  # 缩小箭头长度
        arrow_width = 5  # 缩小线条宽度
        arrow_head_size = 15  # 缩小箭头头部

        print(arrow_length, arrow_width, arrow_head_size)

        # 根据方向计算箭头的起点和终点 - 从圆圈边缘直接开始
        if direction == 'up':
            start_x, start_y = x, y - base_radius  # 从圆圈上边缘开始
            end_x, end_y = x, start_y - arrow_length
            # 箭头头部（向上指）
            head_points = [
                (end_x, end_y),
                (end_x - arrow_head_size, end_y + arrow_head_size),
                (end_x + arrow_head_size, end_y + arrow_head_size)
            ]
        elif direction == 'down':
            start_x, start_y = x, y + base_radius  # 从圆圈下边缘开始
            end_x, end_y = x, start_y + arrow_length
            # 箭头头部（向下指）
            head_points = [
                (end_x, end_y),
                (end_x - arrow_head_size, end_y - arrow_head_size),
                (end_x + arrow_head_size, end_y - arrow_head_size)
            ]
        elif direction == 'left':
            start_x, start_y = x - base_radius, y  # 从圆圈左边缘开始
            end_x, end_y = start_x - arrow_length, y
            # 箭头头部（向左指）
            head_points = [
                (end_x, end_y),
                (end_x + arrow_head_size, end_y - arrow_head_size),
                (end_x + arrow_head_size, end_y + arrow_head_size)
            ]
        elif direction == 'right':
            start_x, start_y = x + base_radius, y  # 从圆圈右边缘开始
            end_x, end_y = start_x + arrow_length, y
            # 箭头头部（向右指）
            head_points = [
                (end_x, end_y),
                (end_x - arrow_head_size, end_y - arrow_head_size),
                (end_x - arrow_head_size, end_y + arrow_head_size)
            ]
        else:
            logger.warning(f"⚠️ Unknown direction: {direction}")
            return

        # 绘制箭头主体线条
        draw.line(
            [start_x, start_y, end_x, end_y],
            fill=self.primary_color,
            width=arrow_width
        )

        # 绘制箭头头部
        draw.polygon(head_points, fill=self.primary_color)

        logger.info(f"🏹 Small direction arrow drawn: {direction} from ({start_x}, {start_y}) to ({end_x}, {end_y})")

    @staticmethod
    def parse_coordinates_and_direction_from_action(action_command: str, device: str) -> None | tuple[
        Any | None, Any | None, None] | tuple[Any | None, Any | None, str | None] | tuple[
                                                                                             Any | None, Any | None, Any | None]:
        """
        从动作命令中解析坐标和方向信息，复用Android工具中的解析逻辑

        Args:
            action_command: 动作命令，如 "click(start_box='(1060,2567)')" 或 "scroll(direction='up')"
            device: 设备标识

        Returns:
            坐标和方向元组 (x, y, direction) 或 None，direction可能为None
        """
        try:
            # 使用现有的Android工具解析动作
            parsed_action = parse_action_with_coordinates(action_command, device)

            if parsed_action is None:
                logger.info(f"📷 No coordinates found in action '{action_command}'")
                return None

            # 提取坐标信息
            action_type = parsed_action.get("action", "")

            # 处理点击和长按动作
            if action_type in ["click", "long_press"]:
                x = parsed_action.get("x")
                y = parsed_action.get("y")
                if x is not None and y is not None:
                    logger.info(f"📍 Parsed coordinates from '{action_command}': ({x}, {y})")
                    return x, y, None

            # 处理拖拽动作（使用起始坐标，计算方向）
            elif action_type == "drag":
                start_x = parsed_action.get("start_x")
                start_y = parsed_action.get("start_y")
                end_x = parsed_action.get("end_x")
                end_y = parsed_action.get("end_y")

                if start_x is not None and start_y is not None:
                    # 计算拖拽方向
                    direction = None
                    if end_x is not None and end_y is not None:
                        dx = end_x - start_x
                        dy = end_y - start_y

                        # 判断主要方向
                        if abs(dx) > abs(dy):
                            direction = "right" if dx > 0 else "left"
                        else:
                            direction = "down" if dy > 0 else "up"

                    logger.info(
                        f"📍 Parsed drag start coordinates from '{action_command}': ({start_x}, {start_y}), direction={direction}")
                    return start_x, start_y, direction

            # 处理滚动动作
            elif action_type == "scroll":
                start_x = parsed_action.get("start_x")
                start_y = parsed_action.get("start_y")
                direction = parsed_action.get("direction")

                if start_x is not None and start_y is not None:
                    logger.info(
                        f"📍 Parsed scroll coordinates from '{action_command}': ({start_x}, {start_y}), direction={direction}")
                    return start_x, start_y, direction

            logger.info(f"📷 No coordinates available for action type '{action_type}' in '{action_command}'")
            return None

        except Exception as e:
            logger.error(f"❌ Error parsing coordinates from '{action_command}': {str(e)}")
            return None

    def _annotate_screenshot_from_action_sync(self, image_path: str, action_command: str,
                                            task_id: str, device: str) -> str:
        """
        同步版本的截图标注方法（内部使用）

        Args:
            image_path: 原始截图路径（相对路径）
            action_command: 动作命令
            task_id: 任务ID
            device: device

        Returns:
            标注后的截图路径（相对路径），如果无法解析坐标则返回原路径
        """
        try:
            # 解析坐标和方向
            result = self.parse_coordinates_and_direction_from_action(action_command, device)

            if result is None:
                logger.info(f"📷 No coordinates found in action '{action_command}', returning original screenshot")
                return image_path

            x, y, direction = result
            logger.info(f"📍 Parsed coordinates from '{action_command}': ({x}, {y}), direction={direction}")

            if "scroll" in action_command or "drag" in action_command:
                screen_width, screen_height = ScreenUtils.get_screen_size(device, task_id)
                if x >= screen_width - 10:
                    x = screen_width - 50

                if x <= 10:
                    x = 50

                if y >= screen_height - 10:
                    y = screen_height - 50

                if y <= 10:
                    y = 50

                coordinates = (x, y)
            else:
                coordinates = (x, y)

            # 标注截图（包含方向信息）
            return self._annotate_screenshot_with_coordinates_sync(image_path, coordinates, direction)

        except Exception as e:
            logger.error(f"❌ Error in _annotate_screenshot_from_action_sync: {str(e)}")
            return image_path

    def _annotate_screenshot_with_coordinates_sync(self, image_path: str, coordinates: Tuple[int, int],
                                                 direction: str = None) -> str:
        """
        同步版本的坐标标注方法（内部使用）

        Args:
            image_path: 原始截图路径（相对路径）
            coordinates: 操作坐标 (x, y)
            direction: 滑动方向（可选），用于绘制箭头

        Returns:
            标注后的截图路径（相对路径）
        """
        try:
            # 获取完整路径
            full_image_path = screenshot_manager.get_screenshot_full_path(image_path)

            if not os.path.exists(full_image_path):
                logger.error(f"❌ Screenshot file not found: {full_image_path}")
                return image_path

            # 打开图片
            with Image.open(full_image_path) as img:
                # 创建绘图对象
                draw = ImageDraw.Draw(img)

                # 获取图片尺寸
                img_width, img_height = img.size
                x, y = coordinates

                # 确保坐标在图片范围内
                if x < 0 or y < 0 or x > img_width or y > img_height:
                    logger.warning(f"⚠️ Coordinates ({x}, {y}) are outside image bounds ({img_width}x{img_height})")
                    # 调整坐标到图片范围内
                    x = max(0, min(x, img_width - 1))
                    y = max(0, min(y, img_height - 1))

                # 绘制多层显眼标记（包含方向箭头）
                self._draw_enhanced_marker(draw, x, y, img_width, img_height, direction)

                # 直接覆盖原始图片文件，不生成新文件
                img.save(full_image_path, 'PNG')

                logger.info(f"📸 Screenshot annotated in place: {image_path}, direction={direction}")

                # 返回原始相对路径
                return image_path

        except Exception as e:
            logger.error(f"❌ Error in _annotate_screenshot_with_coordinates_sync: {str(e)}")
            return image_path

    def annotate_screenshot_from_action_async(self, image_path: str, action_command: str,
                                            task_id: str, device: str) -> None:
        """
        异步版本的截图标注方法

        Args:
            image_path: 原始截图路径（相对路径）
            action_command: 动作命令
            task_id: 任务ID
            device: device
        """
        try:
            annotation_task = {
                'type': 'annotate_from_action',
                'image_path': image_path,
                'action_command': action_command,
                'task_id': task_id,
                'device': device,
                'timestamp': datetime.now().isoformat()
            }

            self._annotation_queue.put_nowait(annotation_task)
            logger.debug(f"[{task_id}] 📝 Screenshot annotation task queued")

        except Exception as e:
            logger.error(f"[{task_id}] ❌ Error queuing annotation task: {str(e)}")

# 创建统一的图像处理器实例
image_processor = ImageProcessor()

# 向后兼容的别名
image_annotator = image_processor
