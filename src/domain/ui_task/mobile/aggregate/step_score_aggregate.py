#!/usr/bin/env python3
"""
步骤评分聚合器
负责处理测试用例步骤评分的业务逻辑
"""

import json
from typing import Tuple, Optional, List, Dict, Any

from loguru import logger

from src.domain.ui_task.mobile.aggregate.prompt.step_score_prompt import build_score_prompt
from src.infra.model import get_chat_model


class StepScoreAggregate:
    """步骤评分聚合器 - 处理步骤评分的核心业务逻辑"""

    def __init__(self):
        self.model = get_chat_model(model_name="default")

    def score_test_steps(self, steps: str) -> Tuple[Optional[List[Dict[str, Any]]], Optional[str]]:
        """
        使用AI对测试用例步骤进行评分

        Args:
            steps: 需要评分的步骤内容

        Returns:
            (评分结果列表, 错误信息) 元组
        """
        try:
            logger.info(f"🔧 StepScoreAggregate: Starting step scoring for: {steps[:100]}...")

            # 构建评分提示
            system_instruction = build_score_prompt(steps)

            # 调用模型
            from langchain_core.messages import SystemMessage

            messages = [
                SystemMessage(content=system_instruction),
                {"role": "user", "content": "开始根据 <评分流程> 为 <用例步骤> 的所有步骤，一行是一个步骤，进行评分和提出优化建议"}
            ]
            output = self.model.invoke(messages)
            ai_result = output.content

            logger.info(f"✅ StepScoreAggregate: AI response: {ai_result}")

            # 解析JSON结果
            score_results = self._parse_score_result(ai_result)
            if score_results is None:
                return None, "Failed to parse AI response as valid JSON"

            # 为每个结果添加id字段
            for i, result in enumerate(score_results, 1):
                result["id"] = i

            logger.info(f"✅ StepScoreAggregate: Step scoring completed successfully")
            return score_results, None

        except Exception as e:
            logger.error(f"❌ StepScoreAggregate: Failed to score steps with AI: {str(e)}")
            return None, str(e)

    def _parse_score_result(self, ai_result: str) -> Optional[List[Dict[str, Any]]]:
        """解析AI返回的评分结果"""
        try:
            # 尝试直接解析JSON
            if ai_result.strip().startswith('['):
                return json.loads(ai_result.strip())

            # 如果不是直接的JSON，尝试提取JSON部分
            start_idx = ai_result.find('[')
            end_idx = ai_result.rfind(']')

            if start_idx != -1 and end_idx != -1 and end_idx > start_idx:
                json_str = ai_result[start_idx:end_idx + 1]
                return json.loads(json_str)

            logger.error(f"❌ StepScoreAggregate: Could not find valid JSON in AI response: {ai_result}")
            return None

        except json.JSONDecodeError as e:
            logger.error(f"❌ StepScoreAggregate: Failed to parse JSON: {str(e)}, AI response: {ai_result}")
            return None
        except Exception as e:
            logger.error(f"❌ StepScoreAggregate: Unexpected error parsing result: {str(e)}")
            return None


# 创建全局实例
step_score_aggregate = StepScoreAggregate()
