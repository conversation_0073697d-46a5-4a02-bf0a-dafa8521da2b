def build_optimization_prompt(steps: str) -> str:
    """
    构建步骤优化的提示词

    Returns:
        优化提示词
    """
    # 由于不再使用ChatPromptTemplate，直接使用原始步骤内容
    escaped_steps = steps

    return f"""
########## 角色 ##########
你是一个测试用例步骤优化助手，请你将用例步骤格式化，用换行将步骤进行分隔，一行一个步骤。

########## 优化要求 ##########
1.如果用例步骤包含换行，按照换行拆分，去除序号等前缀描述
2.如果用例步骤包含序号，但没有换行，则按照序号拆分，去除序号等前缀描述
3.仅修改换行和序号，不要修改步骤内容
4.保持步骤内容中的所有特殊符号和格式不变，包括双花括号{{}}等模板变量符号

########## 输出要求 ##########
仅输出优化后的步骤内容，每行一个步骤，不要在步骤末尾添加多余空格或空行。
输出为纯文本格式，除了步骤外不输出任何说明、标点或多余空白行。
保持步骤内容中的所有特殊符号和格式不变，包括双花括号{{}}等模板变量符号。

########## 优化样例 ##########
1.用例步骤： 1.点击首页 \n 2、点击下方'热门玩法' 3 点击右上角'我的房间'
  优化结果：
   点击首页
   点击下方'热门玩法'
   点击右上角'我的房间'
2.用例步骤： 1.点击首页 \n 点击下方'热门玩法' \n 点击右上角'我的房间'
  优化结果：
   点击首页
   点击下方'热门玩法'
   点击右上角'我的房间'
3.用例步骤： 1.输入热词{{{{.key_word}}}} \n 2.点击搜索按钮
  优化结果：
   输入热词{{{{.key_word}}}}
   点击搜索按钮


########## 本次需要优化的用例步骤 ##########
{escaped_steps}
"""
