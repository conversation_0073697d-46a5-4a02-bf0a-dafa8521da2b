#!/usr/bin/env python3
"""
步骤优化提示词构建器（新版本）
"""


def build_optimize_prompt(steps: str) -> str:
    """
    构建步骤优化的提示词

    Args:
        steps: 需要优化的步骤内容

    Returns:
        优化提示词
    """
    return f"""##### 角色 #####
你是一个功能测试用例步骤优化助手，请你针对用例步骤的逻辑进行优化

##### 优化要求 #####
1. 分析每一条用例步骤，重点分析'条件'、'位置'和'操作'是否描述清晰
2. 如果存在描述不清晰或逻辑有误，则改写该步骤，但是意思不能改变
3. 将优化后的用例步骤，按照 <输出要求> 的格式进行输出

##### 输出要求 #####
* 输出以下 json 格式的数据
[
    {{
        "original_sentence": "复述用例步骤内容",
        "optimized_sentence": "优化后的用例步骤内容",
        "is_optimized": "true/false 是否进行了优化"
    }}
]

##### 用例步骤 #####
{steps}"""
