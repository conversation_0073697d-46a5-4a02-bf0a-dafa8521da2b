#!/usr/bin/env python3
"""
预期结果优化提示词构建器
"""


def build_expected_result_optimize_prompt(expected_result: str) -> str:
    """
    构建预期结果优化的提示词

    Args:
        expected_result: 需要优化的预期结果内容

    Returns:
        优化提示词
    """
    return f"""##### 角色 #####
你是一个功能测试用例预期结果优化助手，请你针对<预期结果>的逻辑进行优化

##### 优化要求 #####
1. 分析每一条用例结果，重点关注结果的 '状态'描述或'对比'条件:
    - 状态: 主要看<预期结果>描述是否在描述一个结果状态而不是一个动作等；
    - 对比: 主要看<预期结果>是否在比对值或图片描述等；
2. 将优化后的用例结果，按照 <输出要求> 的格式进行输出

##### 输出要求 #####
* 输出以下 json 格式的数据
[
    {{
        "original_sentence": "复述用例步骤内容",
        "optimized_sentence": "优化后的用例步骤内容",
        "is_optimized": "true/false 是否进行了优化"
    }}
]

##### 预期结果 #####
{expected_result}"""
