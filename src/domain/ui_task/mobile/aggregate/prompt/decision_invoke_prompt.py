def get_execution_invoke_prompt() -> str:
    return """
############ 执行流程 ##########
1.你需要先进行 <界面分析> 理解界面元素和内容；
2.结合 <界面分析> 结果，执行 <自检流程>；
3.当 <自检流程> 通过，遵循 <执行步骤流程> 进行 <动作决策> 得出"结果"；
4.将结果严格按照 <输出要求> 进行输出最终结果;
5.当完成<测试用例信息>的全部步骤后，执行<预期结果验证>输出最终结果
"""

def get_user_invoke_prompt() -> str:
    return """
####################################
1.请严格按照<执行流程>执行
2.请严格按照<输出要求>输出结果
####################################
"""
