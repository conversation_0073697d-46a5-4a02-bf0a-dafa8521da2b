#!/usr/bin/env python3
"""
步骤优化聚合器（新版本）
负责处理测试用例步骤优化的业务逻辑，返回JSON格式结果
"""

import json
from typing import Tuple, Optional, List, Dict, Any

from loguru import logger

from src.domain.ui_task.mobile.aggregate.prompt.step_optimize_new_prompt import build_optimize_prompt
from src.infra.model import get_chat_model


class StepOptimizeNewAggregate:
    """步骤优化聚合器（新版本） - 处理步骤优化的核心业务逻辑，返回JSON格式"""

    def __init__(self):
        self.model = get_chat_model(model_name="default")

    def optimize_test_steps(self, steps: str) -> Tuple[Optional[List[Dict[str, Any]]], Optional[str]]:
        """
        使用AI优化测试用例步骤

        Args:
            steps: 需要优化的步骤内容

        Returns:
            (优化结果列表, 错误信息) 元组
        """
        try:
            logger.info(f"🔧 StepOptimizeNewAggregate: Starting step optimization for: {steps[:100]}...")

            # 构建优化提示
            system_instruction = build_optimize_prompt(steps)

            # 调用模型
            from langchain_core.messages import SystemMessage

            messages = [
                SystemMessage(content=system_instruction),
                {"role": "user", "content": "开始根据 <优化要求> 为 <用例步骤> 的所有步骤，一行是一个步骤，进行优化"}
            ]
            output = self.model.invoke(messages)
            ai_result = output.content

            logger.info(f"✅ StepOptimizeNewAggregate: AI response: {ai_result}")

            # 解析JSON结果
            optimize_results = self._parse_optimize_result(ai_result)
            if optimize_results is None:
                return None, "Failed to parse AI response as valid JSON"

            # 为每个结果添加id字段
            for i, result in enumerate(optimize_results, 1):
                result["id"] = i

            logger.info(f"✅ StepOptimizeNewAggregate: Step optimization completed successfully")
            return optimize_results, None

        except Exception as e:
            logger.error(f"❌ StepOptimizeNewAggregate: Failed to optimize steps with AI: {str(e)}")
            return None, str(e)



    def _parse_optimize_result(self, ai_result: str) -> Optional[List[Dict[str, Any]]]:
        """解析AI返回的优化结果"""
        try:
            # 尝试直接解析JSON
            if ai_result.strip().startswith('['):
                return json.loads(ai_result.strip())
            
            # 如果不是直接的JSON，尝试提取JSON部分
            start_idx = ai_result.find('[')
            end_idx = ai_result.rfind(']')
            
            if start_idx != -1 and end_idx != -1 and end_idx > start_idx:
                json_str = ai_result[start_idx:end_idx + 1]
                return json.loads(json_str)
            
            logger.error(f"❌ StepOptimizeNewAggregate: Could not find valid JSON in AI response: {ai_result}")
            return None
            
        except json.JSONDecodeError as e:
            logger.error(f"❌ StepOptimizeNewAggregate: Failed to parse JSON: {str(e)}, AI response: {ai_result}")
            return None
        except Exception as e:
            logger.error(f"❌ StepOptimizeNewAggregate: Unexpected error parsing result: {str(e)}")
            return None


# 创建全局实例
step_optimize_new_aggregate = StepOptimizeNewAggregate()
