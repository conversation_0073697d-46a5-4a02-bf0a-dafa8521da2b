#!/usr/bin/env python3
"""
决策Agent

负责分析截图、生成思考过程和决定下一步动作（不包含坐标）
"""

import base64
import json
import time
from typing import Tuple, Any

from langchain.output_parsers import OutputFixingParser
from langchain_core.output_parsers import PydanticOutputParser
from loguru import logger
from pydantic import BaseModel, Field

from src.domain.ui_task.mobile.aggregate.prompt.decision_definition_prompt import *
from src.domain.ui_task.mobile.aggregate.prompt.decision_invoke_prompt import *
from src.domain.ui_task.mobile.android.screenshot_manager import \
    screenshot_manager, convert_screenshot_to_base64
from src.domain.ui_task.mobile.repo.do.State import DeploymentState
from src.domain.ui_task.mobile.utils.exception_handler import TaskExceptionHandler, AgentExceptionHandler
from src.infra.model import get_chat_model


class InvalidStepIndexError(Exception):
    """当current_step_name不是有效格式时抛出的异常"""
    pass


class DecisionResponse(BaseModel):
    """决策Agent响应的数据模型"""
    self_check: str = Field(default="", description="自检流程的自检结果")
    interface_analysis: str = Field(default="", description="当前界面分析")
    current_step_name: str = Field(default="", description="当前正在执行的步骤名称（包含序号），从测试用例信息获取")
    action_decision: str = Field(default="", description="动作决策结果")
    instruction: str = Field(default="", description="操作指令")
    action: str = Field(default="", description="具体动作命令")


class DecisionAgent:
    """决策Agent - 负责分析和决策"""

    def __init__(self):
        self.model = get_chat_model(model_name="default")

        # 创建输出解析器
        self.pydantic_parser = PydanticOutputParser(pydantic_object=DecisionResponse)

        # 尝试使用专门的修复模型，如果不存在则使用默认模型
        self.output_parser = OutputFixingParser.from_llm(
            parser=self.pydantic_parser,
            llm=get_chat_model(model_name="fix")
        )

    def analyze_and_decide(self, state: DeploymentState,
                           before_screenshot_path: str) -> tuple:
        """
            分析截图和测试用例上下文，生成思考过程和决定下一步动作

            Args:
                state: 当前状态
                before_screenshot_path: 截图的base64数据

            Returns:
                Tuple[parsed_fields, action_line]: 解析的JSON字段、动作命令
            """
        # 构建消息
        task_id = state["task_id"]
        try:
            image_data_base64 = convert_screenshot_to_base64(before_screenshot_path, task_id)
            messages = self._build_complete_test_case_messages(state, image_data_base64)
            start_time = time.time()

            model_response_obj = self.model.invoke(messages)
            model_response = model_response_obj.content

            logger.info(f"[task_id: {task_id}] 决策模型完整响应: \n {model_response}")
            # 解析JSON响应
            parsed_fields, action_line = self._parse_json_response(model_response, state)
            # 立即记录决策日志
            self._log_decision_immediately(parsed_fields, action_line, task_id)
            # 决策结束时间
            end_time = time.time()
            logger.info(f"[task_id: {task_id}] 决策耗时: {end_time - start_time:.2f}s")
            return parsed_fields, action_line
        except Exception as e:
            return AgentExceptionHandler.handle_decision_agent_exception(task_id, e, state)

    def _parse_json_response(self, model_response: str, state: DeploymentState) -> \
            Tuple[Dict[str, Any], str]:
        """
            解析模型的JSON响应，提取各个字段
            使用OutputFixingParser来纠正格式错误的JSON

            Args:
                model_response: 模型的完整响应
                state: 当前状态

            Returns:
                Tuple[parsed_fields, action_line]: 解析的字段字典、动作命令

            Raises:
                Exception: 当JSON解析和修复都失败时抛出异常
            """
        task_id = state['task_id']

        try:
            # 首先尝试直接解析JSON
            json_data = json.loads(model_response.strip())
            logger.info(f"[{task_id}] ✅ Direct JSON parsing successful")

        except json.JSONDecodeError as e:
            logger.warning(f"[{task_id}] ⚠️ Direct JSON parsing failed: {str(e)}")
            logger.info(f"[{task_id}] 🔧 Attempting to fix JSON format using OutputFixingParser...")

            try:
                # 使用OutputFixingParser来修复JSON格式
                parsed_response: DecisionResponse = self.output_parser.parse(model_response)
                json_data = parsed_response.model_dump()
                logger.info(f"[{task_id}] ✅ JSON format fixed successfully using OutputFixingParser")

            except Exception as fix_error:
                logger.error(f"[{task_id}] ❌ OutputFixingParser failed: {str(fix_error)}")
                logger.error(f"[{task_id}] Raw response: {model_response}")

                # 使用统一的异常处理方法
                TaskExceptionHandler.update_task_status_to_failed(
                    task_id,
                    f"JSON parsing and fixing failed in decision agent: {str(fix_error)}"
                )

                # 重新抛出异常，让外层异常处理机制处理
                raise fix_error

        # 解析和验证字段，失败直接抛出异常
        current_step_name_raw = json_data.get("current_step_name", "")
        if not current_step_name_raw:
            raise InvalidStepIndexError("current_step_name is empty or missing")

        # 提取步骤序号和名称
        import re
        step_pattern = r'^(\d+)[.)、\-\s）]+(.*)$'
        match = re.match(step_pattern, current_step_name_raw.strip())

        # 检查是否是 finished 或 failed 动作
        action_command = json_data.get("action", "")
        is_final_action = action_command.startswith("finished") or action_command.startswith("failed")

        if not match:
            # 如果是 finished/failed 动作且格式不正确，尝试使用执行记忆中最后一次的步骤名称
            if is_final_action and state:
                last_step_name = self._get_last_execution_step_name(state)
                if last_step_name:
                    logger.info(f"[{state.get('task_id', '')}] 🔄 finished/failed动作使用执行记忆中的步骤名称: {last_step_name}")
                    current_step_name_raw = last_step_name
                    match = re.match(step_pattern, current_step_name_raw.strip())

            if not match:
                raise InvalidStepIndexError(
                    f"current_step_name does not contain valid step index format: '{current_step_name_raw}'")

        try:
            current_step_index_extracted = int(match.group(1))
            current_step_name_clean = match.group(2).strip()
        except (ValueError, IndexError) as e:
            raise InvalidStepIndexError(
                f"Failed to extract valid step index from current_step_name: {current_step_name_raw}")

        if current_step_index_extracted <= 0:
            raise InvalidStepIndexError(f"Extracted step index must be positive: {current_step_index_extracted}")

        # 根据步骤索引替换步骤名称（如果有state）
        current_step_name_full = current_step_name_raw  # 保存完整的步骤名称（包含序号）
        if state:
            task_steps = state.get("task_steps", [])
            array_index = current_step_index_extracted - 1
            if 0 <= array_index < len(task_steps):
                accurate_step_name = task_steps[array_index]
                current_step_name_full = accurate_step_name  # 使用完整的步骤名称
                accurate_step_match = re.match(step_pattern, accurate_step_name.strip())
                if accurate_step_match:
                    current_step_name_clean = accurate_step_match.group(2).strip()
                else:
                    current_step_name_clean = accurate_step_name.strip()
                # 更新状态中的当前步骤索引
                state["current_step_index"] = array_index

        # 构建返回的字段字典
        parsed_fields = {
            "self_check": json_data.get("self_check", ""),
            "interface_analysis": json_data.get("interface_analysis", ""),
            "current_step_index": current_step_index_extracted,
            "current_step_name": current_step_name_clean,
            "current_step_name_full": current_step_name_full,  # 新增：完整的步骤名称（包含序号）
            "action_decision": json_data.get("action_decision", ""),
            "instruction": json_data.get("instruction", ""),
            "action": json_data.get("action", "")
        }

        action_line = json_data.get("action", "")
        return parsed_fields, action_line



    def _build_complete_test_case_messages(self, state: DeploymentState,
                                           image_data_base64: str) -> list:

        execution_records, image_records, has_execution_history = self.get_history_parameters(state)

        decision_prompt = get_decision_definition_prompt(state, execution_records, has_execution_history)

        invoke_prompt = get_execution_invoke_prompt()

        messages = [
            {
                "role": "system",
                "content": decision_prompt
            },
            {
                "role": "system",
                "content": invoke_prompt
            }
        ]

        # 添加期望结果图片（如果存在）
        expected_result_images = self._get_overall_expected_images(state)
        if expected_result_images:
            messages.append({
                "role": "user",
                "content": expected_result_images
            })

        if has_execution_history:
            # 构建历史截图内容列表
            content_list = []

            # 添加历史截图（最近5轮）
            for i, record in enumerate(image_records):
                before_screenshot_path = record.get("before_screenshot")
                if before_screenshot_path and before_screenshot_path != "":
                    try:
                        full_path = screenshot_manager.get_screenshot_full_path(before_screenshot_path)
                        with open(full_path, "rb") as f:
                            before_image_content = f.read()
                        before_image_data_base64 = base64.b64encode(before_image_content).decode("utf-8")

                        execution_count = record.get("execution_count", i + 1)
                        content_list.extend([
                            {
                                "type": "text",
                                "text": f"第{execution_count}轮界面截图"
                            },
                            {
                                "type": "image_url",
                                "image_url": {
                                    "url": f"data:image/png;base64,{before_image_data_base64}",
                                    "detail": "high"
                                }
                            }
                        ])
                    except Exception as e:
                        logger.warning(f"Failed to load historical screenshot {before_screenshot_path}: {str(e)}")
                        continue

            # 如果有历史截图，添加包含所有图片的消息
            if content_list:
                messages.append(
                    {
                        "role": "user",
                        "content": "########## 执行记忆界面截图 ##########"
                    }
                )
                content_list.extend([
                    {
                        "type": "text",
                        "text": "########## 当前轮界面截图 ##########"
                    },
                    {
                        "type": "image_url",
                        "image_url": {
                            "url": f"data:image/png;base64,{image_data_base64}",
                            "detail": "high"
                        }
                    }
                ])
                messages.append({
                    "role": "user",
                    "content": content_list
                })
            else:
                # 如果没有历史截图，只添加当前截图
                messages.append({
                    "role": "user",
                    "content": [
                        {
                            "type": "text",
                            "text": "########## 当前轮界面截图 ##########"
                        },
                        {
                            "type": "image_url",
                            "image_url": {
                                "url": f"data:image/png;base64,{image_data_base64}",
                                "detail": "high"
                            }
                        }
                    ]
                })
        else:
            messages.append({
                "role": "user",
                "content": [
                    {
                        "type": "text",
                        "text": "########## 当前轮界面截图 ##########"
                    },
                    {
                        "type": "image_url",
                        "image_url": {
                            "url": f"data:image/png;base64,{image_data_base64}"
                        }
                    }
                ]
            })

        messages.append({
            "role": "user",
            "content": get_user_invoke_prompt()
        })
        return messages

    def _get_overall_expected_images(self, state: DeploymentState) -> list:
        """
        获取整体期望结果图片列表，用于消息拼接

        Args:
            state: 当前状态

        Returns:
            期望结果图片的消息内容列表
        """
        try:
            overall_expected_result = state.get("overall_expected_result", {})
            if not overall_expected_result:
                return []

            # 获取期望结果图片列表
            expected_images = overall_expected_result.get("images", [])
            if not expected_images:
                return []

            # 构建期望结果图片的消息内容
            content_items = [
                {
                    "type": "text",
                    "text": "########## 预期结果图片列表 ##########"
                }
            ]

            for i, image_info in enumerate(expected_images, 1):
                image_name = image_info.get("image_name", f"expected_image_{i}")
                image_path = image_info.get("image_path", "")

                if not image_path:
                    continue

                try:
                    # 读取图片文件并转换为base64
                    from src.domain.ui_task.mobile.android.screenshot_manager import convert_screenshot_to_base64
                    image_base64 = convert_screenshot_to_base64(image_path, state.get("task_id", ""))
                    if image_base64:
                        content_items.extend([
                            {
                                "type": "text",
                                "text": f"第{i}张预期结果图片：{image_name}"
                            },
                            {
                                "type": "image_url",
                                "image_url": {
                                    "url": f"data:image/png;base64,{image_base64}",
                                    "detail": "high"
                                }
                            }
                        ])
                except Exception as e:
                    task_id = state.get("task_id", "")
                    logger.warning(f"[{task_id}] ⚠️ Failed to load expected result image {image_path}: {str(e)}")
                    continue

            # 如果没有成功加载任何图片，返回空列表
            if len(content_items) <= 1:
                return []

            return content_items

        except Exception as e:
            task_id = state.get("task_id", "")
            logger.error(f"[{task_id}] ❌ Error getting overall expected images: {str(e)}")
            return []

    @staticmethod
    def get_history_parameters(state) -> tuple[list[Any], list[Any], bool]:
        history = state.get("history", [])
        execution_records = [r for r in history if
                             r.get("action") == "enhanced_get_location" and
                             (r.get("decision_content") or r.get(
                                 "decision_fields") or r.get("model_response"))]
        image_records = execution_records[-2:] if len(execution_records) > 2 else execution_records
        has_execution_history = len(execution_records) > 0
        print(len(execution_records), len(image_records))
        return execution_records, image_records, has_execution_history

    def _get_last_execution_step_name(self, state: DeploymentState) -> str:
        """
        获取执行记忆中最后一次执行的步骤名称

        Args:
            state: 当前状态

        Returns:
            最后一次执行的步骤名称，如果没有找到则返回空字符串
        """
        try:
            history = state.get("history", [])

            # 从后往前查找最近的执行记录
            for record in reversed(history):
                # 查找决策执行记录
                if record.get("action") == "enhanced_get_location":
                    decision_fields = record.get("decision_fields", {})
                    if decision_fields:
                        step_name_full = decision_fields.get("current_step_name_full", "")
                        if step_name_full:
                            return step_name_full

                        # 如果没有 current_step_name_full，尝试构建
                        step_index = decision_fields.get("current_step_index", 0)
                        step_name = decision_fields.get("current_step_name", "")
                        if step_index > 0 and step_name:
                            return f"{step_index}.{step_name}"

            # 如果没有找到决策记录，尝试从 task_steps 中获取当前步骤
            current_step_index = state.get("current_step_index", 0)
            task_steps = state.get("task_steps", [])
            if 0 <= current_step_index < len(task_steps):
                return task_steps[current_step_index]

            # 如果 current_step_index 超出范围，尝试获取最后一个步骤
            if task_steps:
                return task_steps[-1]

            return ""

        except Exception as e:
            task_id = state.get("task_id", "")
            logger.warning(f"[{task_id}] ⚠️ Failed to get last execution step name: {str(e)}")
            return ""

    @staticmethod
    def _log_decision_immediately(parsed_fields: Dict[str, Any], action_line: str, task_id: str):
        """异步记录决策日志"""
        try:
            from src.domain.ui_task.mobile.service.async_log_service import async_log_service

            async_log_service.log_decision_async(task_id, parsed_fields, action_line)

        except Exception as e:
            logger.warning(f"[{task_id}] Failed to queue decision log: {str(e)}")
