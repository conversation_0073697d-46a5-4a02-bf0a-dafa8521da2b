#!/usr/bin/env python3
"""
键盘管理服务

负责UI任务执行过程中的ADB键盘管理业务逻辑
"""

from loguru import logger

from src.domain.ui_task.mobile.android.keyboard_manager import get_keyboard_manager, cleanup_keyboard_manager
from src.domain.ui_task.mobile.service.execution_log_service import ExecutionLogService, LogLevel
from src.domain.ui_task.mobile.service.task_persistence_service import task_persistence_service


class KeyboardService:
    """键盘管理服务"""

    def _log_system_message(self, task_id: str, message: str, level=None):
        """记录系统日志，避免循环导入"""
        if not task_id:
            return
        try:
            from src.domain.ui_task.mobile.service.task_persistence_service import task_persistence_service
            if level:
                log_entry = ExecutionLogService.create_system_log(message, level)
            else:
                log_entry = ExecutionLogService.create_system_log(message)
            task_persistence_service.append_execution_log_entries(task_id, [log_entry])
        except ImportError:
            # 避免循环导入，如果导入失败就跳过日志记录
            logger.debug(f"[{task_id}] Skip system log due to import issue: {message}")

    def setup_adb_keyboards_for_task(self, task_id: str, device_id: str) -> bool:
        """
        为任务设置ADB键盘
        
        Args:
            task_id: 任务ID
            device_id: 设备ID
            
        Returns:
            是否设置成功
        """
        try:
            logger.info(f"[{task_id}] 🎹 Setting up ADB keyboards for device: {device_id}")

            # 记录键盘设置开始的系统日志
            self._log_system_message(task_id, f"开始设置ADB键盘: {device_id}")

            # 检查设备连接状态
            if not self._check_device_connection(device_id):
                logger.warning(f"[{task_id}] ⚠️ Device {device_id} not connected, skipping keyboard setup")
                self._log_system_message(task_id, f"设备未连接，跳过键盘设置: {device_id}", LogLevel.WARNING)
                return False

            # 获取键盘管理器并设置键盘
            keyboard_manager = get_keyboard_manager(device_id)
            success = keyboard_manager.setup_adb_keyboards()

            if success:
                logger.info(f"[{task_id}] ✅ ADB keyboards setup successful")
                self._log_system_message(task_id, f"ADB键盘设置成功: {device_id}")
            else:
                logger.warning(f"[{task_id}] ⚠️ ADB keyboards setup failed")
                self._log_system_message(task_id, f"ADB键盘设置失败: {device_id}", LogLevel.WARNING)

            return success
            
        except Exception as e:
            logger.error(f"[{task_id}] ❌ Error setting up ADB keyboards: {str(e)}")
            return False
    
    def cleanup_adb_keyboards_for_task(self, task_id: str) -> bool:
        """
        为任务清理ADB键盘

        Args:
            task_id: 任务ID

        Returns:
            是否清理成功
        """
        try:
            # 获取任务信息
            task = task_persistence_service.get_task_by_task_id(task_id)
            if not task:
                logger.warning(f"[{task_id}] ⚠️ Task not found, skipping keyboard cleanup")
                return False

            device_id = task.device_id
            task_status = task.status.value if hasattr(task.status, 'value') else str(task.status)

            logger.info(f"[{task_id}] 🧹 Cleaning up ADB keyboards for device: {device_id} (task status: {task_status})")

            # 记录键盘清理开始的系统日志
            self._log_system_message(task_id, f"开始清理ADB键盘: {device_id}")

            # 无论任务状态如何，都要清理键盘设置
            # 因为任务结束时（无论成功还是失败）都需要恢复原始键盘
            cleanup_keyboard_manager(device_id)
            logger.info(f"[{task_id}] ✅ ADB keyboards cleanup completed")

            # 记录键盘清理完成的系统日志
            self._log_system_message(task_id, f"ADB键盘清理完成: {device_id}")

            return True

        except Exception as e:
            logger.error(f"[{task_id}] ❌ Error cleaning up ADB keyboards: {str(e)}")
            return False
    
    def cleanup_adb_keyboards_for_device(self, device_id: str) -> bool:
        """
        为设备清理ADB键盘（不检查任务状态）
        
        Args:
            device_id: 设备ID
            
        Returns:
            是否清理成功
        """
        try:
            logger.info(f"[device: {device_id}] 🧹 Cleaning up ADB keyboards")
            cleanup_keyboard_manager(device_id)
            logger.info(f"[device: {device_id}] ✅ ADB keyboards cleanup completed")
            return True
        except Exception as e:
            logger.error(f"[device: {device_id}] ❌ Error cleaning up ADB keyboards: {str(e)}")
            return False
    
    def _check_device_connection(self, device_id: str) -> bool:
        """
        检查设备连接状态
        
        Args:
            device_id: 设备ID
            
        Returns:
            设备是否连接
        """
        import subprocess
        
        try:
            result = subprocess.run(
                f"adb -s {device_id} shell echo 'connected'",
                shell=True,
                capture_output=True,
                text=True,
                timeout=5
            )
            return result.returncode == 0 and "connected" in result.stdout
        except Exception as e:
            logger.error(f"Error checking device connection {device_id}: {str(e)}")
            return False


# 创建全局服务实例
keyboard_service = KeyboardService()
