#!/usr/bin/env python3
"""
异步日志记录服务

将日志记录操作改为异步执行，避免影响主流程进度
"""

import threading
from concurrent.futures import ThreadPoolExecutor
from datetime import datetime
from queue import Queue, Empty
from typing import Dict, Any, List

from loguru import logger


class AsyncLogService:
    """异步日志记录服务"""
    
    _instance = None
    _lock = threading.Lock()
    
    def __new__(cls):
        if cls._instance is None:
            with cls._lock:
                if cls._instance is None:
                    cls._instance = super().__new__(cls)
        return cls._instance
    
    def __init__(self):
        if hasattr(self, '_initialized'):
            return
            
        self._initialized = True
        self.log_queue = Queue()
        self.executor = ThreadPoolExecutor(max_workers=2, thread_name_prefix="AsyncLog")
        self.is_running = True
        
        # 启动后台日志处理线程
        self.worker_thread = threading.Thread(target=self._process_logs, daemon=True)
        self.worker_thread.start()
        
        logger.info("🚀 AsyncLogService initialized")
    
    def _process_logs(self):
        """后台线程处理日志队列"""
        while self.is_running:
            try:
                # 从队列中获取日志任务，超时1秒
                log_task = self.log_queue.get(timeout=1.0)
                
                if log_task is None:  # 停止信号
                    break
                
                # 在线程池中执行日志记录
                future = self.executor.submit(self._execute_log_task, log_task)
                
                # 标记任务完成
                self.log_queue.task_done()
                
            except Empty:
                # 队列为空，继续等待
                continue
            except Exception as e:
                logger.warning(f"⚠️ Error in log processing thread: {str(e)}")
    
    def _execute_log_task(self, log_task: Dict[str, Any]):
        """执行具体的日志记录任务"""
        try:
            task_type = log_task.get('type')
            task_id = log_task.get('task_id', 'unknown')
            
            if task_type == 'decision_log':
                self._write_decision_log(log_task)
            elif task_type == 'execution_log':
                self._write_execution_log(log_task)
            elif task_type == 'step_log':
                self._write_step_log(log_task)
            elif task_type == 'error_log':
                self._write_error_log(log_task)
            elif task_type == 'append_log_entries':
                self._append_log_entries_direct(log_task)
            elif task_type == 'update_action_end_time':
                self._update_action_end_time(log_task)
            elif task_type == 'update_action_end_time_by_id':
                self._update_action_end_time_by_id(log_task)
            else:
                logger.warning(f"[{task_id}] Unknown log task type: {task_type}")
                
        except Exception as e:
            task_id = log_task.get('task_id', 'unknown')
            logger.warning(f"[{task_id}] ⚠️ Failed to execute log task: {str(e)}")
    
    def _write_decision_log(self, log_task: Dict[str, Any]):
        """写入决策日志"""
        try:
            from src.domain.ui_task.mobile.service.task_persistence_service import task_persistence_service
            from src.domain.ui_task.mobile.service.execution_log_service import ExecutionLogService
            
            task_id = log_task['task_id']
            parsed_fields = log_task['parsed_fields']
            action_command = log_task['action_command']
            
            step_name = parsed_fields.get("current_step_name", "")
            action_decision = parsed_fields.get("action_decision", "")
            
            decision_log = ExecutionLogService.create_decision_log(step_name, action_decision, action_command)

            # 直接调用数据库操作，避免递归
            self._append_single_log_entry(task_id, decision_log)
            
        except Exception as e:
            task_id = log_task.get('task_id', 'unknown')
            logger.warning(f"[{task_id}] ⚠️ Failed to write decision log: {str(e)}")
    
    def _write_execution_log(self, log_task: Dict[str, Any]):
        """写入执行日志"""
        try:
            from src.domain.ui_task.mobile.service.task_persistence_service import task_persistence_service
            from src.domain.ui_task.mobile.service.execution_log_service import ExecutionLogService
            
            task_id = log_task['task_id']
            thought_content = log_task['thought_content']
            
            execution_log = ExecutionLogService.create_execution_log(thought_content)

            # 直接调用数据库操作，避免递归
            self._append_single_log_entry(task_id, execution_log)
            
        except Exception as e:
            task_id = log_task.get('task_id', 'unknown')
            logger.warning(f"[{task_id}] ⚠️ Failed to write execution log: {str(e)}")
    
    def _write_step_log(self, log_task: Dict[str, Any]):
        """写入步骤日志"""
        try:
            from src.domain.ui_task.mobile.service.task_persistence_service import task_persistence_service
            from src.domain.ui_task.mobile.service.execution_log_service import ExecutionLogService
            
            task_id = log_task['task_id']
            log_type = log_task['log_type']
            message = log_task['message']
            
            if log_type == 'start':
                log_entry = ExecutionLogService.create_system_log(f"开始执行步骤: {message}")
            elif log_type == 'completion':
                log_entry = ExecutionLogService.create_system_log(f"步骤完成: {message}")
            elif log_type == 'action':
                log_entry = ExecutionLogService.create_execution_log(f"动作执行: {message}")
            elif log_type == 'ai_decision':
                # 截取AI响应的前500个字符以避免日志过长
                truncated_message = message[:500] + "..." if len(message) > 500 else message
                log_entry = ExecutionLogService.create_decision_log(
                    step_name=log_task.get('step_name', ''),
                    action_decision=truncated_message,
                    action="AI步骤决策"
                )
            else:
                log_entry = ExecutionLogService.create_system_log(message)
            
            # 直接调用数据库操作，避免递归
            self._append_single_log_entry(task_id, log_entry)
            
        except Exception as e:
            task_id = log_task.get('task_id', 'unknown')
            logger.warning(f"[{task_id}] ⚠️ Failed to write step log: {str(e)}")
    
    def _write_error_log(self, log_task: Dict[str, Any]):
        """写入错误日志"""
        try:
            from src.domain.ui_task.mobile.service.task_persistence_service import task_persistence_service
            from src.domain.ui_task.mobile.service.execution_log_service import ExecutionLogService
            
            task_id = log_task['task_id']
            error_message = log_task['error_message']
            
            log_entry = ExecutionLogService.create_error_log(error_message)

            # 直接调用数据库操作，避免递归
            self._append_single_log_entry(task_id, log_entry)
            
        except Exception as e:
            task_id = log_task.get('task_id', 'unknown')
            logger.warning(f"[{task_id}] ⚠️ Failed to write error log: {str(e)}")

    def _append_log_entries_direct(self, log_task: Dict[str, Any]):
        """直接追加日志条目到数据库"""
        try:
            import json
            from src.domain.ui_task.mobile.repo.ui_task_repository import UITaskRepository
            from src.domain.ui_task.mobile.service.execution_log_service import ExecutionLogService

            task_id = log_task['task_id']
            log_entries = log_task['log_entries']

            # 获取任务仓库
            task_repo = UITaskRepository()

            # 获取当前任务
            current_task = task_repo.get_task_by_task_id(task_id)
            if not current_task:
                logger.warning(f"[{task_id}] ⚠️ Task not found: {task_id}")
                return

            # 获取当前日志列表
            current_logs = []
            if current_task.execution_log:
                if isinstance(current_task.execution_log, str):
                    try:
                        current_logs = json.loads(current_task.execution_log)
                    except json.JSONDecodeError:
                        # 如果解析失败，说明是旧的文本格式，进行转换
                        current_logs = ExecutionLogService.parse_existing_text_log(current_task.execution_log)

            # 合并日志
            updated_logs = current_logs + log_entries

            # 更新日志（Repository层会自动转换为JSON字符串）
            task_repo.update_execution_log(task_id, updated_logs)

        except Exception as e:
            task_id = log_task.get('task_id', 'unknown')
            logger.warning(f"[{task_id}] ⚠️ Failed to append log entries directly: {str(e)}")

    def _append_single_log_entry(self, task_id: str, log_entry: Dict[str, Any]):
        """追加单个日志条目的辅助方法"""
        try:
            import json
            from src.domain.ui_task.mobile.repo.ui_task_repository import UITaskRepository
            from src.domain.ui_task.mobile.service.execution_log_service import ExecutionLogService

            # 获取任务仓库
            task_repo = UITaskRepository()

            # 获取当前任务
            current_task = task_repo.get_task_by_task_id(task_id)
            if not current_task:
                logger.warning(f"[{task_id}] ⚠️ Task not found: {task_id}")
                return

            # 获取当前日志列表
            current_logs = []
            if current_task.execution_log:
                if isinstance(current_task.execution_log, str):
                    try:
                        current_logs = json.loads(current_task.execution_log)
                    except json.JSONDecodeError:
                        # 如果解析失败，说明是旧的文本格式，进行转换
                        current_logs = ExecutionLogService.parse_existing_text_log(current_task.execution_log)

            # 追加新日志条目
            updated_logs = current_logs + [log_entry]

            # 更新日志（Repository层会自动转换为JSON字符串）
            task_repo.update_execution_log(task_id, updated_logs)

        except Exception as e:
            logger.warning(f"[{task_id}] ⚠️ Failed to append single log entry: {str(e)}")

    def _update_action_end_time(self, log_task: Dict[str, Any]):
        """更新action结束时间"""
        try:
            from src.domain.ui_task.mobile.repo.ui_task_repository import DBSessionContext
            from src.domain.ui_task.mobile.repo.dao import UITaskAction
            from datetime import datetime

            task_id = log_task['task_id']
            end_time_str = log_task['end_time']
            end_time = datetime.fromisoformat(end_time_str)

            # 获取当前任务最新的运行中的action记录
            with DBSessionContext() as session:
                action = session.query(UITaskAction).filter(
                    UITaskAction.task_id == task_id,
                    UITaskAction.status == "running"
                ).order_by(UITaskAction.id.desc()).first()

                if action:
                    # 只更新结束时间，不改变状态（状态由complete_task_action统一管理）
                    action.end_time = end_time
                    action.updated_at = datetime.now()
                    session.commit()
                    logger.debug(f"[{task_id}] ⏰ Updated action end_time for action {action.id}")

        except Exception as e:
            task_id = log_task.get('task_id', 'unknown')
            logger.warning(f"[{task_id}] ⚠️ Failed to update action end_time: {str(e)}")

    def _update_action_end_time_by_id(self, log_task: Dict[str, Any]):
        """根据action_id更新action结束时间"""
        try:
            from src.domain.ui_task.mobile.repo.ui_task_repository import DBSessionContext
            from src.domain.ui_task.mobile.repo.dao import UITaskAction
            from datetime import datetime

            action_id = log_task['action_id']
            end_time_str = log_task['end_time']
            end_time = datetime.fromisoformat(end_time_str)

            # 根据action_id直接更新
            with DBSessionContext() as session:
                action = session.query(UITaskAction).filter(UITaskAction.id == action_id).first()

                if action:
                    # 只更新结束时间，不改变状态（状态由complete_task_action统一管理）
                    action.end_time = end_time
                    action.updated_at = datetime.now()
                    session.commit()
                    logger.debug(f"⏰ Updated action end_time for action {action_id}")

        except Exception as e:
            action_id = log_task.get('action_id', 'unknown')
            logger.warning(f"⚠️ Failed to update action end_time by id {action_id}: {str(e)}")

    def log_decision_async(self, task_id: str, parsed_fields: Dict[str, Any], action_command: str):
        """异步记录决策日志"""
        log_task = {
            'type': 'decision_log',
            'task_id': task_id,
            'parsed_fields': parsed_fields,
            'action_command': action_command,
            'timestamp': datetime.now().isoformat()
        }
        
        try:
            self.log_queue.put_nowait(log_task)
        except Exception as e:
            logger.warning(f"[{task_id}] ⚠️ Failed to queue decision log: {str(e)}")
    
    def log_execution_async(self, task_id: str, thought_content: str):
        """异步记录执行日志"""
        log_task = {
            'type': 'execution_log',
            'task_id': task_id,
            'thought_content': thought_content,
            'timestamp': datetime.now().isoformat()
        }
        
        try:
            self.log_queue.put_nowait(log_task)
        except Exception as e:
            logger.warning(f"[{task_id}] ⚠️ Failed to queue execution log: {str(e)}")
    
    def log_step_async(self, task_id: str, log_type: str, message: str, step_name: str = ""):
        """异步记录步骤日志"""
        log_task = {
            'type': 'step_log',
            'task_id': task_id,
            'log_type': log_type,
            'message': message,
            'step_name': step_name,
            'timestamp': datetime.now().isoformat()
        }
        
        try:
            self.log_queue.put_nowait(log_task)
        except Exception as e:
            logger.warning(f"[{task_id}] ⚠️ Failed to queue step log: {str(e)}")
    
    def log_error_async(self, task_id: str, error_message: str):
        """异步记录错误日志"""
        log_task = {
            'type': 'error_log',
            'task_id': task_id,
            'error_message': error_message,
            'timestamp': datetime.now().isoformat()
        }

        try:
            self.log_queue.put_nowait(log_task)
        except Exception as e:
            logger.warning(f"[{task_id}] ⚠️ Failed to queue error log: {str(e)}")

    def append_execution_log_entries_async(self, task_id: str, log_entries: List[Dict[str, Any]]):
        """异步追加执行日志条目 - 直接对应task_persistence_service.append_execution_log_entries"""
        log_task = {
            'type': 'append_log_entries',
            'task_id': task_id,
            'log_entries': log_entries,
            'timestamp': datetime.now().isoformat()
        }

        try:
            self.log_queue.put_nowait(log_task)
        except Exception as e:
            logger.warning(f"[{task_id}] ⚠️ Failed to queue log entries: {str(e)}")

    def shutdown(self):
        """关闭异步日志服务"""
        logger.info("🛑 Shutting down AsyncLogService...")
        
        self.is_running = False
        
        # 发送停止信号
        self.log_queue.put(None)
        
        # 等待工作线程结束
        if self.worker_thread.is_alive():
            self.worker_thread.join(timeout=5.0)
        
        # 关闭线程池
        self.executor.shutdown(wait=True)
        
        logger.info("✅ AsyncLogService shutdown complete")
    
    def get_queue_size(self) -> int:
        """获取当前队列大小"""
        return self.log_queue.qsize()


# 全局异步日志服务实例
async_log_service = AsyncLogService()
