#!/usr/bin/env python3
"""
预期结果优化服务 - 协调预期结果优化的业务流程
"""

from typing import Tuple, Optional, List, Dict, Any
from loguru import logger

from src.domain.ui_task.mobile.aggregate.expected_result_optimize_aggregate import expected_result_optimize_aggregate


class ExpectedResultOptimizeService:
    """预期结果优化服务 - 负责协调预期结果优化的业务流程"""

    def __init__(self):
        """初始化服务"""
        pass

    def optimize_expected_result(self, expected_result: str) -> Tuple[Optional[List[Dict[str, Any]]], Optional[str]]:
        """
        优化测试用例预期结果

        Args:
            expected_result: 需要优化的预期结果内容

        Returns:
            (优化结果列表, 错误信息) 元组
        """
        try:
            logger.info(f"🔧 ExpectedResultOptimizeService: Starting expected result optimization for: {expected_result[:100]}...")

            # 调用聚合器处理业务逻辑
            result, error = expected_result_optimize_aggregate.optimize_expected_result(expected_result)

            if error:
                logger.error(f"❌ ExpectedResultOptimizeService: Expected result optimization failed: {error}")
                return None, error

            logger.info(f"✅ ExpectedResultOptimizeService: Expected result optimization completed successfully")
            return result, None

        except Exception as e:
            logger.error(f"❌ ExpectedResultOptimizeService: Failed to optimize expected result: {str(e)}")
            return None, str(e)


# 创建全局实例
expected_result_optimize_service = ExpectedResultOptimizeService()
