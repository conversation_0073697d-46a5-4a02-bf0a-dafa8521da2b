#!/usr/bin/env python3
"""
视频服务 - 处理视频文件的流式上传、存储和AI解析
简化版本，专注于一次性流式上传功能和AI视频解析
"""

import base64
import shutil
import threading
import uuid
from datetime import datetime, timedelta
from pathlib import Path
from typing import Optional, Dict, Tuple

from loguru import logger
from openai import OpenAI

from config.globalconfig import get_or_create_settings_ins


class VideoService:
    """视频服务类 - 简化版，专注于流式上传和AI解析"""

    def __init__(self):
        # 从配置文件读取设置
        config = get_or_create_settings_ins()

        # 视频存储基础目录
        self.base_dir = Path(config.paths.video_base_dir)
        self.base_dir.mkdir(parents=True, exist_ok=True)

        # 流式上传临时目录
        self.temp_dir = Path(config.paths.video_temp_dir)
        self.temp_dir.mkdir(parents=True, exist_ok=True)

        # 活跃的流式上传会话
        self.active_streams: Dict[str, Dict] = {}

        # 会话锁，防止并发写入
        self.session_locks: Dict[str, threading.Lock] = {}

        # 从配置读取参数
        self.session_timeout = config.paths.video_session_timeout
        self.max_upload_size = config.paths.video_max_upload_size  # 50MB
        self.hmac_secret = config.paths.video_hmac_secret

        # 豆包AI配置
        self.doubao_api_key = config.paths.doubao_api_key
        self.doubao_base_url = config.paths.doubao_base_url
        self.doubao_model = config.paths.doubao_model

        # 初始化OpenAI客户端
        self.ai_client = None
        if self.doubao_api_key:
            self.ai_client = OpenAI(
                base_url=self.doubao_base_url,
                api_key=self.doubao_api_key
            )

    def video_exists(self, task_id: str) -> bool:
        """
        检查任务视频是否存在

        Args:
            task_id: 任务ID

        Returns:
            是否存在
        """
        file_path = self.base_dir / f"{task_id}.mp4"
        return file_path.exists()

    def delete_task_video(self, task_id: str) -> bool:
        """
        删除任务视频文件

        Args:
            task_id: 任务ID

        Returns:
            是否删除成功
        """
        try:
            file_path = self.base_dir / f"{task_id}.mp4"
            if file_path.exists():
                file_path.unlink()
                logger.info(f"[{task_id}] ✅ Video deleted successfully: {file_path}")
                return True
            else:
                logger.warning(f"[{task_id}] ⚠️ Video file not found: {file_path}")
                return True  # 文件不存在也算删除成功
        except Exception as e:
            logger.error(f"[{task_id}] ❌ Failed to delete video: {str(e)}")
            return False



    def start_stream_upload(self, expected_size: Optional[int] = None) -> Tuple[Optional[str], Optional[str]]:
        """
        开始流式上传会话

        Args:
            expected_size: 预期文件大小（字节）

        Returns:
            (会话ID, 错误信息) 元组
        """
        try:
            # 检查预期大小限制
            if expected_size and expected_size > self.max_upload_size:
                return None, f"Expected size {expected_size} exceeds maximum allowed {self.max_upload_size}"

            # 生成简单的UUID会话ID
            video_id = uuid.uuid4().hex

            # 创建临时文件路径
            temp_file_path = self.temp_dir / f"{video_id}.mp4"

            # 创建会话锁
            self.session_locks[video_id] = threading.Lock()

            # 初始化会话信息
            current_time = datetime.now()
            self.active_streams[video_id] = {
                "video_id": video_id,
                "temp_file_path": temp_file_path,
                "file_handle": None,
                "start_time": current_time,
                "last_activity": current_time,
                "expires_at": current_time + timedelta(seconds=self.session_timeout),
                "total_size": 0,
                "expected_size": expected_size,
                "chunk_count": 0,
                "is_completed": False,
                "is_cancelled": False
            }

            # 打开文件句柄用于写入
            self.active_streams[video_id]["file_handle"] = open(temp_file_path, "wb")

            logger.info(f"[video_id: {video_id}] 🎬 Started stream upload session")
            return video_id, None

        except Exception as e:
            logger.error(f"❌ Failed to start stream upload: {str(e)}")
            return None, str(e)

    async def append_stream_chunk(self, video_id: str, chunk_data: bytes, chunk_index: Optional[int] = None) -> Tuple[bool, Optional[str]]:
        """
        追加流式数据块

        Args:
            video_id: 上传会话ID
            chunk_data: 数据块
            chunk_index: 数据块序号（用于日志记录）

        Returns:
            (是否成功, 错误信息) 元组
        """
        try:
            if video_id not in self.active_streams:
                return False, "Stream session not found"

            session = self.active_streams[video_id]

            # 基础会话验证
            if session["video_id"] != video_id:
                return False, "Invalid session ID"

            # 检查会话是否已完成或取消
            if session["is_completed"]:
                return False, "Stream session already completed"

            if session["is_cancelled"]:
                return False, "Stream session was cancelled"

            # 检查会话是否过期
            if datetime.now() > session["expires_at"]:
                return False, "Stream session expired"

            # 检查总大小限制
            if session["total_size"] + len(chunk_data) > self.max_upload_size:
                return False, f"Upload would exceed maximum size {self.max_upload_size}"

            # 使用锁防止并发写入
            with self.session_locks[video_id]:
                file_handle = session["file_handle"]
                if not file_handle or file_handle.closed:
                    return False, "File handle is closed or invalid"

                # 写入数据块
                file_handle.write(chunk_data)
                file_handle.flush()  # 确保数据写入磁盘

                # 更新会话信息
                session["total_size"] += len(chunk_data)
                session["chunk_count"] += 1
                session["last_activity"] = datetime.now()

                logger.debug(
                    f"[video_id: {video_id}] 📦 Appended chunk {chunk_index or session['chunk_count']}: "
                    f"{len(chunk_data)} bytes, total: {session['total_size']} bytes"
                )

                return True, None

        except Exception as e:
            logger.error(f"[video_id: {video_id}] ❌ Failed to append stream chunk: {str(e)}")
            return False, str(e)

    def complete_stream_upload(self, video_id: str) -> Tuple[Optional[str], Optional[str]]:
        """
        完成流式上传

        Args:
            video_id: 上传会话ID

        Returns:
            (最终文件路径, 错误信息) 元组
        """
        try:
            if video_id not in self.active_streams:
                return None, "Stream session not found"

            session = self.active_streams[video_id]
            temp_file_path = session["temp_file_path"]

            # 检查会话状态
            if session["is_completed"]:
                return None, "Stream session already completed"

            if session["is_cancelled"]:
                return None, "Stream session was cancelled"

            # 使用锁确保线程安全
            with self.session_locks.get(video_id, threading.Lock()):
                # 关闭文件句柄
                if session["file_handle"] and not session["file_handle"].closed:
                    session["file_handle"].close()

                # 检查临时文件是否存在
                if not temp_file_path.exists():
                    return None, f"Temporary file not found: {temp_file_path}"

                # 验证文件大小
                actual_size = temp_file_path.stat().st_size
                if actual_size != session["total_size"]:
                    logger.warning(
                        f"[video_id: {video_id}] Size mismatch: expected {session['total_size']}, "
                        f"actual {actual_size}"
                    )

                # 创建按日期分组的目录结构
                current_date = datetime.now()
                date_folder = current_date.strftime("%Y%m%d")  # 格式：20250728
                date_dir = self.base_dir / date_folder
                date_dir.mkdir(parents=True, exist_ok=True)

                # 移动到最终位置，使用session_id作为文件名
                final_file_path = date_dir / f"{video_id}.mp4"

                # 如果最终文件已存在，先删除
                if final_file_path.exists():
                    final_file_path.unlink()

                # 移动文件
                shutil.move(str(temp_file_path), str(final_file_path))

                # 标记会话完成
                session["is_completed"] = True
                session["completion_time"] = datetime.now()

                logger.info(
                    f"[video_id: {video_id}] ✅ Stream upload completed: {final_file_path}, "
                    f"size: {actual_size} bytes, chunks: {session['chunk_count']}"
                )

                # 清理会话
                self._cleanup_session(video_id)

                return str(final_file_path), None

        except Exception as e:
            logger.error(f"[video_id: {video_id}] ❌ Failed to complete stream upload: {str(e)}")
            return None, str(e)

    def cancel_stream_upload(self, video_id: str) -> Tuple[bool, Optional[str]]:
        """
        取消流式上传（用于错误处理）

        Args:
            video_id: 上传会话ID

        Returns:
            (是否成功, 错误信息) 元组
        """
        try:
            if video_id not in self.active_streams:
                return True, None  # 会话不存在也算取消成功

            session = self.active_streams[video_id]

            # 使用锁确保线程安全
            with self.session_locks.get(video_id, threading.Lock()):
                # 标记为已取消
                session["is_cancelled"] = True
                session["cancellation_time"] = datetime.now()

                # 关闭文件句柄
                if session["file_handle"] and not session["file_handle"].closed:
                    session["file_handle"].close()

                # 删除临时文件
                temp_file_path = session["temp_file_path"]
                if temp_file_path.exists():
                    temp_file_path.unlink()

                logger.info(
                    f"[video_id: {video_id}] 🚫 Stream upload cancelled, "
                    f"uploaded: {session['total_size']} bytes, chunks: {session['chunk_count']}"
                )

                # 清理会话
                self._cleanup_session(video_id)

                return True, None

        except Exception as e:
            logger.error(f"[video_id: {video_id}] ❌ Failed to cancel stream upload: {str(e)}")
            return False, str(e)

    def _cleanup_session(self, video_id: str):
        """
        清理会话

        Args:
            video_id: 会话ID
        """
        if video_id in self.active_streams:
            session = self.active_streams[video_id]

            # 确保文件句柄关闭
            if session["file_handle"] and not session["file_handle"].closed:
                session["file_handle"].close()

            # 删除临时文件（如果存在）
            temp_file_path = session.get("temp_file_path")
            if temp_file_path and temp_file_path.exists():
                try:
                    temp_file_path.unlink()
                except Exception as e:
                    logger.warning(f"Failed to delete temp file {temp_file_path}: {str(e)}")

            # 删除会话
            del self.active_streams[video_id]

            # 删除会话锁
            if video_id in self.session_locks:
                del self.session_locks[video_id]

    def cleanup_old_videos(self, days_to_keep: int = 2) -> int:
        """
        清理指定天数前的视频文件

        Args:
            days_to_keep: 保留天数，默认2天

        Returns:
            删除的文件数量
        """
        try:
            deleted_count = 0
            current_date = datetime.now()
            cutoff_date = current_date - timedelta(days=days_to_keep)

            logger.info(f"🧹 Starting video cleanup, removing videos older than {cutoff_date.strftime('%Y-%m-%d')}")

            # 遍历视频基础目录下的所有日期文件夹
            for date_folder in self.base_dir.iterdir():
                if not date_folder.is_dir():
                    continue

                # 检查文件夹名称是否为日期格式（YYYYMMDD）
                folder_name = date_folder.name
                if len(folder_name) != 8 or not folder_name.isdigit():
                    continue

                try:
                    # 解析文件夹日期
                    folder_date = datetime.strptime(folder_name, "%Y%m%d")

                    # 如果文件夹日期早于截止日期，删除整个文件夹
                    if folder_date < cutoff_date:
                        logger.info(f"🗑️ Deleting old video folder: {date_folder}")

                        # 统计文件数量
                        video_files = list(date_folder.glob("*.mp4"))
                        folder_file_count = len(video_files)

                        # 删除文件夹及其内容
                        shutil.rmtree(date_folder)
                        deleted_count += folder_file_count

                        logger.info(f"✅ Deleted {folder_file_count} videos from {folder_name}")

                except ValueError:
                    # 文件夹名称不是有效的日期格式，跳过
                    logger.warning(f"⚠️ Skipping invalid date folder: {folder_name}")
                    continue

            logger.info(f"🎉 Video cleanup completed, deleted {deleted_count} old videos")
            return deleted_count

        except Exception as e:
            logger.error(f"❌ Failed to cleanup old videos: {str(e)}")
            return 0

    def get_video_by_session_id(self, video_id: str) -> Optional[Path]:
        """
        根据session_id获取视频文件路径

        Args:
            video_id: 会话ID

        Returns:
            视频文件路径，不存在返回None
        """
        try:
            # 遍历所有日期文件夹查找视频文件
            for date_folder in self.base_dir.iterdir():
                if not date_folder.is_dir():
                    continue

                video_file = date_folder / f"{video_id}.mp4"
                if video_file.exists():
                    return video_file

            return None

        except Exception as e:
            logger.error(f"❌ Failed to find video by video_id {video_id}: {str(e)}")
            return None

    def encode_video_to_base64(self, video_path: Path) -> Optional[str]:
        """
        将视频文件编码为base64字符串

        Args:
            video_path: 视频文件路径

        Returns:
            base64编码的视频数据，失败返回None
        """
        try:
            with open(video_path, 'rb') as video_file:
                video_data = video_file.read()
                base64_data = base64.b64encode(video_data).decode('utf-8')

            logger.info(f"✅ Video encoded to base64: {len(base64_data)} characters")
            return base64_data

        except Exception as e:
            logger.error(f"❌ Failed to encode video to base64: {str(e)}")
            return None

    def analyze_video_with_ai(self, video_id: str, verification_mode: str = "step_by_step") -> Tuple[Optional[str], Optional[str]]:
        """
        使用AI分析视频并生成测试用例步骤

        Args:
            video_id: 视频会话ID
            verification_mode: 验证模式 ("step_by_step" 或 "aggregation")

        Returns:
            (分析结果, 错误信息) 元组
        """
        try:
            if not self.ai_client:
                return None, "AI client not initialized, please check doubao_api_key configuration"

            # 根据session_id查找视频文件
            video_path = self.get_video_by_session_id(video_id)
            if not video_path:
                return None, f"Video not found for video_id: {video_id}"

            logger.info(f"[video_id: {video_id}] 🎬 Starting AI video analysis: {video_path}")

            # 检查文件大小
            file_size = video_path.stat().st_size
            if file_size > self.max_upload_size:
                return None, f"Video file too large: {file_size} bytes, max allowed: {self.max_upload_size} bytes"

            # 将视频编码为base64
            base64_video = self.encode_video_to_base64(video_path)
            if not base64_video:
                return None, "Failed to encode video to base64"

            # 根据verification_mode构建不同的AI请求
            if verification_mode == "step_by_step":
                system_instruction = """
########## 角色 ##########                
你是一个测试用例步骤生成专家，你能够根据用户操作视频，解析出关键步骤。

########## 任务要求 ##########
请仔细观看这个操作视频，然后生成详细的测试用例步骤。
1. 按照操作顺序生成步骤
2. 每个步骤要包含具体的操作内容，需要排除等待广告、弹窗等无关操作
3. 每个步骤要包含期望的结果
4. 步骤要足够详细，能够被自动化测试工具执行
5. 必须严格按照以下JSON格式输出 

########## 输出格式要求 ##########

必须严格按照以下JSON格式输出,请生成完整的JSON格式测试用例步骤:
{{
  "task_step_by_step": [
    {
      "step": "字段说明：步骤名称,需要提炼出精简的步骤名称，例如：点击xxx、填写xxx、长按xxx、向上/下/左/右/滑动、向上/下/左/右/拉动xxx",
      "expect_result": {
        "text": "期望结果的文字描述",
      }
    }
  ]
}}

"""
            else:  # aggregation mode
                system_instruction = """
########## 角色 ##########
你是一个测试用例步骤生成专家，你能够根据用户操作视频，解析出关键步骤。

########## 任务要求 ##########
请仔细观看这个操作视频，然后生成聚合模式的测试用例步骤。
1. 将所有操作步骤解析成独立的步骤列表
2. 每个步骤要包含步骤名称和等待时间
3. 步骤要足够详细，能够指导自动化测试工具执行
4. 步骤中要包含所有关键操作的细节, 需要排除等待广告、弹窗等无关操作
5. 必须严格按照以下JSON格式输出

########## 输出格式要求 ##########
必须严格按照以下JSON格式输出,请生成完整的JSON格式测试用例步骤:
{{
  "task_step_by_step": [
    {{
      "step": "步骤名称，如：点击登录按钮",
      "expect_result": {{
        "wait_time": 2.5
      }}
    }},
    {{
      "step": "步骤名称，如：输入用户名",
      "expect_result": {{
        "wait_time": 2.5
      }}
    }}
  ]
}}
```

请生成完整的JSON格式步骤列表。"""

            logger.info(f"[video_id: {video_id}] 🤖 Calling Doubao AI with base64 video data, mode: {verification_mode}")

            messages = []
            messages.append({
                "role": "system",
                "content": system_instruction
            })
            messages.append({
                "role": "user",
                "content": [
                    {
                        "type": "video_url",
                        "video_url": {
                             "url": f"data:video/mp4;base64,{base64_video}"
                        }
                    }
                ]
            })

            response = self.ai_client.chat.completions.create(
                model=self.doubao_model,
                messages=messages,
                temperature=0
            )

            # 提取AI响应
            ai_result = response.choices[0].message.content
            logger.info(
                f"[video_id: {video_id}] ✅ AI video analysis completed successfully, result: {ai_result}")
            return ai_result, None
        except Exception as e:
          logger.error(f"[video_id: {video_id}] ❌ Failed to analyze video with AI: {str(e)}")
          return None, str(e)


# 创建全局实例
video_service = VideoService()
