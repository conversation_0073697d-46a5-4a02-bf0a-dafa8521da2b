#!/usr/bin/env python3
"""
步骤评分服务 - 协调步骤评分的业务流程
"""

from typing import Tuple, Optional, List, Dict, Any
from loguru import logger

from src.domain.ui_task.mobile.aggregate.step_score_aggregate import step_score_aggregate


class StepScoreService:
    """步骤评分服务 - 负责协调步骤评分的业务流程"""

    def __init__(self):
        """初始化服务"""
        pass

    def score_steps(self, steps: str) -> Tuple[Optional[List[Dict[str, Any]]], Optional[str]]:
        """
        评分测试用例步骤

        Args:
            steps: 需要评分的步骤内容

        Returns:
            (评分结果列表, 错误信息) 元组
        """
        try:
            logger.info(f"🔧 StepScoreService: Starting step scoring for: {steps[:100]}...")

            # 调用聚合器处理业务逻辑
            result, error = step_score_aggregate.score_test_steps(steps)

            if error:
                logger.error(f"❌ StepScoreService: Step scoring failed: {error}")
                return None, error

            logger.info(f"✅ StepScoreService: Step scoring completed successfully")
            return result, None

        except Exception as e:
            logger.error(f"❌ StepScoreService: Failed to score steps: {str(e)}")
            return None, str(e)


# 创建全局实例
step_score_service = StepScoreService()
