#!/usr/bin/env python3
"""
步骤优化服务（新版本） - 协调步骤优化的业务流程，返回JSON格式结果
"""

from typing import Tuple, Optional, List, Dict, Any
from loguru import logger

from src.domain.ui_task.mobile.aggregate.step_optimize_new_aggregate import step_optimize_new_aggregate


class StepOptimizeNewService:
    """步骤优化服务（新版本） - 负责协调步骤优化的业务流程，返回JSON格式结果"""

    def __init__(self):
        """初始化服务"""
        pass

    def optimize_steps(self, steps: str) -> Tuple[Optional[List[Dict[str, Any]]], Optional[str]]:
        """
        优化测试用例步骤

        Args:
            steps: 需要优化的步骤内容

        Returns:
            (优化结果列表, 错误信息) 元组
        """
        try:
            logger.info(f"🔧 StepOptimizeNewService: Starting step optimization for: {steps[:100]}...")

            # 调用聚合器处理业务逻辑
            result, error = step_optimize_new_aggregate.optimize_test_steps(steps)

            if error:
                logger.error(f"❌ StepOptimizeNewService: Step optimization failed: {error}")
                return None, error

            logger.info(f"✅ StepOptimizeNewService: Step optimization completed successfully")
            return result, None

        except Exception as e:
            logger.error(f"❌ StepOptimizeNewService: Failed to optimize steps: {str(e)}")
            return None, str(e)


# 创建全局实例
step_optimize_new_service = StepOptimizeNewService()
