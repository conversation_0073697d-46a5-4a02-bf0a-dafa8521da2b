#!/usr/bin/env python3
"""
任务停止管理器

负责管理基于taskid的任务停止状态
"""

import threading
from typing import Dict, Set
from loguru import logger
from datetime import datetime
from src.schema.action_types import ExecutionStatus


class TaskStopManager:
    """任务停止管理器 - 单例模式"""
    
    _instance = None
    _lock = threading.Lock()
    
    def __new__(cls):
        if cls._instance is None:
            with cls._lock:
                if cls._instance is None:
                    cls._instance = super(TaskStopManager, cls).__new__(cls)
                    cls._instance._initialized = False
        return cls._instance
    
    def __init__(self):
        if not self._initialized:
            self._stopped_tasks: Set[str] = set()
            self._task_info: Dict[str, Dict] = {}
            self._lock = threading.Lock()
            self._initialized = True
    
    def stop_task(self, task_id: str) -> bool:
        """
        停止指定的任务（仅标记内存状态，不更新数据库）

        Args:
            task_id: 任务ID

        Returns:
            是否成功标记为停止
        """
        with self._lock:
            if task_id in self._task_info:
                self._stopped_tasks.add(task_id)
                self._task_info[task_id]["stop_time"] = datetime.now().isoformat()
                self._task_info[task_id]["status"] = "stopped"
                logger.info(f"[{task_id}] 🛑 Task {task_id} marked for stop in memory")
                return True
            else:
                logger.warning(f"[{task_id}] ⚠️ Task {task_id} not found in active tasks")
                return False
    
    def is_task_stopped(self, task_id: str) -> bool:
        """
        检查任务是否被停止
        
        Args:
            task_id: 任务ID
            
        Returns:
            任务是否被停止
        """
        with self._lock:
            return task_id in self._stopped_tasks
    
    def register_task(self, task_id: str, task_name: str = "") -> None:
        """
        注册新任务
        
        Args:
            task_id: 任务ID
            task_name: 任务名称
        """
        with self._lock:
            self._task_info[task_id] = {
                "task_name": task_name,
                "start_time": datetime.now().isoformat(),
                "status": ExecutionStatus.PROCESSING.value
            }
            logger.info(f"[{task_id}] 📝 Task {task_id} registered: {task_name}")
    
    def unregister_task(self, task_id: str) -> None:
        """
        注销任务（任务完成或失败时调用）
        
        Args:
            task_id: 任务ID
        """
        with self._lock:
            self._stopped_tasks.discard(task_id)
            if task_id in self._task_info:
                del self._task_info[task_id]
                logger.info(f"[{task_id}] 🗑️ Task {task_id} unregistered")


# 全局单例实例
task_stop_manager = TaskStopManager()
