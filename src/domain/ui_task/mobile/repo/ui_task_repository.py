#!/usr/bin/env python3
"""
UI任务Repository - 数据库操作层
"""

from datetime import datetime
from typing import Optional, Dict, Any, Type, List, Union

from loguru import logger
from sqlalchemy import desc

from src.domain.ui_task.mobile.repo.dao import UITask, UITaskAction
from src.infra.clients.mysql.orm import DBSessionContext
from src.schema.action_types import ExecutionStatus


class UITaskRepository:
    """UI任务数据库操作Repository"""

    def __init__(self):
        pass

    def create_task(self, task_data: Dict[str, Any]) -> UITask:
        """
        创建新任务
        
        Args:
            task_data: 任务数据字典
            
        Returns:
            创建的任务对象
        """
        with DBSessionContext() as session:
            task = UITask(**task_data)
            session.add(task)
            session.commit()
            session.refresh(task)

            # 验证创建后的状态
            logger.info(f"[{task.task_id}] 🔍 Task created with status: '{task.status}' (type: {type(task.status)})")

            return task

    def get_task_by_task_id(self, task_id: str) -> Optional[UITask]:
        """
        根据task_id获取任务
        
        Args:
            task_id: 任务ID
            
        Returns:
            任务对象或None
        """
        with DBSessionContext() as session:
            return session.query(UITask).filter(UITask.task_id == task_id).first()

    def get_task_by_id(self, id: int) -> Optional[UITask]:
        """
        根据主键ID获取任务
        
        Args:
            id: 主键ID
            
        Returns:
            任务对象或None
        """
        with DBSessionContext() as session:
            return session.query(UITask).filter(UITask.id == id).first()

    def update_task_status(self, task_id: str, status,
                          error_message: str = None, end_time: datetime = None) -> bool:
        """
        更新任务状态
        
        Args:
            task_id: 任务ID
            status: 新状态
            error_message: 错误信息（可选）
            end_time: 结束时间（可选）
            
        Returns:
            是否更新成功
        """
        with DBSessionContext() as session:
            task = session.query(UITask).filter(UITask.task_id == task_id).first()
            if not task:
                return False
                
            # 记录状态变更
            old_status = task.status
            logger.info(f"[{task_id}] 🔄 Status change: {old_status} -> {status}")

            task.status = status if isinstance(status, str) else getattr(status, 'value', str(status))
            task.updated_at = datetime.now()

            if error_message:
                task.error_message = error_message

            if end_time:
                task.end_time = end_time
                if task.start_time:
                    task.execution_duration = (end_time - task.start_time).total_seconds()

            session.commit()
            return True

    def start_task_execution(self, task_id: str) -> bool:
        """
        开始任务执行
        
        Args:
            task_id: 任务ID
            
        Returns:
            是否更新成功
        """
        with DBSessionContext() as session:
            task = session.query(UITask).filter(UITask.task_id == task_id).first()
            if not task:
                return False
                
            task.start_execution()
            session.commit()
            return True

    def complete_task_execution(self, task_id: str, success: bool = True,
                               error_message: str = None, execution_log: str = None, is_system_error: bool = False) -> bool:
        """
        完成任务执行
        
        Args:
            task_id: 任务ID
            success: 是否成功
            error_message: 错误信息（可选）
            execution_log: 执行日志（可选）
            
        Returns:
            是否更新成功
        """
        with DBSessionContext() as session:
            task = session.query(UITask).filter(UITask.task_id == task_id).first()
            if not task:
                return False
                
            task.complete_execution(success, error_message, is_system_error)
            if execution_log:
                task.execution_log = execution_log
                
            session.commit()
            return True

    def update_task_image_path(self, task_id: str, image_path: str) -> bool:
        """
        更新任务的截图路径

        Args:
            task_id: 任务ID
            image_path: 截图路径

        Returns:
            是否更新成功
        """
        with DBSessionContext() as session:
            task = session.query(UITask).filter(UITask.task_id == task_id).first()
            if not task:
                return False

            task.image_path = image_path
            task.updated_at = datetime.now()
            session.commit()
            return True

    def update_execution_log(self, task_id: str, execution_log: Union[str, List[Dict[str, Any]]]) -> bool:
        """
        更新执行日志

        Args:
            task_id: 任务ID
            execution_log: 执行日志（支持文本格式或JSON列表格式，最终存储为字符串）

        Returns:
            是否更新成功
        """
        with DBSessionContext() as session:
            task = session.query(UITask).filter(UITask.task_id == task_id).first()
            if not task:
                return False

            # 如果是列表格式，转换为JSON字符串
            if isinstance(execution_log, list):
                import json
                task.execution_log = json.dumps(execution_log, ensure_ascii=False)
            else:
                # 如果是字符串，直接存储
                task.execution_log = execution_log

            task.updated_at = datetime.now()
            session.commit()
            return True

    def get_tasks_by_status(self, status, limit: int = 100) -> list[Type[UITask]]:
        """
        根据状态获取任务列表
        
        Args:
            status: 任务状态
            limit: 限制数量
            
        Returns:
            任务列表
        """
        with DBSessionContext() as session:
            status_value = status if isinstance(status, str) else getattr(status, 'value', str(status))
            return session.query(UITask).filter(UITask.status == status_value)\
                         .order_by(desc(UITask.created_at)).limit(limit).all()

    def get_running_tasks(self, limit: int = 100) -> list[Type[UITask]]:
        """
        获取正在运行的任务

        Args:
            limit: 限制数量

        Returns:
            任务列表
        """
        return self.get_tasks_by_status(ExecutionStatus.PROCESSING, limit)

    def get_recent_tasks(self, limit: int = 50) -> list[Type[UITask]]:
        """
        获取最近的任务
        
        Args:
            limit: 限制数量
            
        Returns:
            任务列表
        """
        with DBSessionContext() as session:
            return session.query(UITask).order_by(desc(UITask.created_at)).limit(limit).all()

    def delete_task(self, task_id: str) -> bool:
        """
        删除任务（软删除或硬删除）
        
        Args:
            task_id: 任务ID
            
        Returns:
            是否删除成功
        """
        with DBSessionContext() as session:
            task = session.query(UITask).filter(UITask.task_id == task_id).first()
            if not task:
                return False
                
            session.delete(task)
            session.commit()
            return True

    def increment_execution_count(self, task_id: str) -> bool:
        """
        增加执行次数
        
        Args:
            task_id: 任务ID
            
        Returns:
            是否更新成功
        """
        with DBSessionContext() as session:
            task = session.query(UITask).filter(UITask.task_id == task_id).first()
            if not task:
                return False
                
            task.updated_at = datetime.now()
            session.commit()
            return True


class UITaskActionRepository:
    """UI任务动作数据库操作Repository"""

    def __init__(self):
        pass

    def create_action(self, action_data: Dict[str, Any]) -> UITaskAction:
        """
        创建新动作
        
        Args:
            action_data: 动作数据字典
            
        Returns:
            创建的动作对象
        """
        with DBSessionContext() as session:
            action = UITaskAction(**action_data)
            session.add(action)
            session.commit()
            session.refresh(action)
            return action

    def get_actions_by_task_id(self, task_id: str) -> list[Type[UITaskAction]]:
        """
        根据任务ID获取所有动作

        Args:
            task_id: 任务UUID

        Returns:
            动作列表
        """
        with DBSessionContext() as session:
            return session.query(UITaskAction).filter(UITaskAction.task_id == task_id)\
                         .order_by(UITaskAction.id).all()

    def update_action_status(self, action_id: int, status: str,
                             error_message: str = None) -> bool:
        """
        更新动作状态
        
        Args:
            action_id: 动作ID
            status: 新状态
            error_message: 错误信息（可选）

        Returns:
            是否更新成功
        """
        with DBSessionContext() as session:
            action = session.query(UITaskAction).filter(UITaskAction.id == action_id).first()
            if not action:
                return False
                
            action.status = status
            action.updated_at = datetime.now()
            
            if error_message:
                action.error_message = error_message
            session.commit()
            return True

    def complete_action_execution(self, action_id: int, success: bool = True,
                                  error_message: str = None, terminate: bool = False) -> bool:
        """
        完成动作执行

        Args:
            action_id: 动作ID
            success: 是否成功
            error_message: 错误信息（可选）
            terminate: 是否为用户终止

        Returns:
            是否更新成功
        """
        with DBSessionContext() as session:
            action = session.query(UITaskAction).filter(UITaskAction.id == action_id).first()
            if not action:
                return False

            action.complete_execution(success, error_message, terminate)
            session.commit()
            return True

    def update_action_extra_info(self, action_id: int, before_screenshot: str = None,
                                verification_result: Dict = None, final_action_command: str = None) -> bool:
        """
        更新动作的额外信息

        Args:
            action_id: 动作ID
            before_screenshot: 执行前截图路径
            verification_result: 验证结果
            final_action_command: 最终的动作命令（带坐标）

        Returns:
            是否更新成功
        """
        with DBSessionContext() as session:
            action = session.query(UITaskAction).filter(UITaskAction.id == action_id).first()
            if not action:
                return False

            if before_screenshot:
                # 存储执行前截图路径
                action.image_path = before_screenshot

            if verification_result:
                action.verification_result = verification_result

            if final_action_command:
                action.action = final_action_command

            action.updated_at = datetime.now()
            session.commit()
            return True

    def get_action_by_id(self, action_id: int) -> Optional[UITaskAction]:
        """
        根据动作ID获取动作记录

        Args:
            action_id: 动作ID

        Returns:
            动作对象或None
        """
        with DBSessionContext() as session:
            return session.query(UITaskAction).filter(UITaskAction.id == action_id).first()

    def delete_action(self, action_id: int) -> bool:
        """
        删除动作记录

        Args:
            action_id: 动作ID

        Returns:
            是否删除成功
        """
        with DBSessionContext() as session:
            action = session.query(UITaskAction).filter(UITaskAction.id == action_id).first()
            if not action:
                return False

            session.delete(action)
            session.commit()
            return True

    def delete_actions_by_task_id(self, task_id: str) -> bool:
        """
        删除任务的所有动作

        Args:
            task_id: 任务UUID

        Returns:
            是否删除成功
        """
        with DBSessionContext() as session:
            actions = session.query(UITaskAction).filter(UITaskAction.task_id == task_id).all()
            if not actions:
                return True  # 没有动作也算成功

            for action in actions:
                session.delete(action)
            session.commit()
            return True

    def delete_actions_by_ids(self, action_ids: List[int]) -> Dict[str, Any]:
        """
        批量删除指定ID的动作记录

        Args:
            action_ids: 动作ID列表

        Returns:
            删除结果字典，包含成功删除的数量和失败的ID列表
        """
        if not action_ids:
            return {
                "success": True,
                "deleted_count": 0,
                "total_count": 0,
                "failed_ids": []
            }

        deleted_count = 0
        failed_ids = []
        total_count = len(action_ids)

        with DBSessionContext() as session:
            for action_id in action_ids:
                try:
                    action = session.query(UITaskAction).filter(UITaskAction.id == action_id).first()
                    if action:
                        session.delete(action)
                        deleted_count += 1
                    else:
                        failed_ids.append(action_id)
                except Exception as e:
                    logger.error(f"❌ Error deleting action {action_id}: {str(e)}")
                    failed_ids.append(action_id)

            try:
                session.commit()
            except Exception as e:
                logger.error(f"❌ Error committing batch delete: {str(e)}")
                session.rollback()
                # 如果提交失败，所有删除都失败
                return {
                    "success": False,
                    "deleted_count": 0,
                    "total_count": total_count,
                    "failed_ids": action_ids
                }

        return {
            "success": deleted_count > 0,
            "deleted_count": deleted_count,
            "total_count": total_count,
            "failed_ids": failed_ids
        }
