"""Initial migration

Revision ID: f5da9c413017
Revises: 
Create Date: 2025-08-08 10:43:49.070677

"""
from typing import Sequence, Union

from alembic import op
import sqlalchemy as sa
from sqlalchemy.dialects import mysql

# revision identifiers, used by Alembic.
revision: str = 'f5da9c413017'
down_revision: Union[str, Sequence[str], None] = None
branch_labels: Union[str, Sequence[str], None] = None
depends_on: Union[str, Sequence[str], None] = None


def upgrade() -> None:
    """Upgrade schema."""
    # ### commands auto generated by Alembic - please adjust! ###
    op.alter_column('ui_task', 'status',
               existing_type=mysql.ENUM('processing', 'succeed', 'failed', 'terminate', collation='utf8mb4_general_ci'),
               type_=sa.String(length=32),
               nullable=True,
               comment='任务状态: processing/succeed/failed/terminate')
    op.alter_column('ui_task_action', 'status',
               existing_type=mysql.ENUM('RUNNING', 'COMPLETED', 'FAILED', collation='utf8mb4_general_ci'),
               type_=sa.Text(),
               existing_comment='执行状态: running/completed/failed',
               existing_nullable=False)
    # ### end Alembic commands ###


def downgrade() -> None:
    """Downgrade schema."""
    # ### commands auto generated by Alembic - please adjust! ###
    op.alter_column('ui_task_action', 'status',
               existing_type=sa.Text(),
               type_=mysql.ENUM('RUNNING', 'COMPLETED', 'FAILED', collation='utf8mb4_general_ci'),
               existing_comment='执行状态: running/completed/failed',
               existing_nullable=False)
    op.alter_column('ui_task', 'status',
               existing_type=sa.String(length=32),
               type_=mysql.ENUM('processing', 'succeed', 'failed', 'terminate', collation='utf8mb4_general_ci'),
               nullable=False,
               comment=None,
               existing_comment='任务状态: processing/succeed/failed/terminate')
    # ### end Alembic commands ###
