FROM cr.ttyuyin.com/public/python:3.12

ENV TZ=Asia/Shanghai
RUN ln -snf /usr/share/zoneinfo/$TZ /etc/localtime && echo $TZ > /etc/timezone

# 安装 ADB 工具和其他必要的系统依赖
RUN apt-get update && apt-get install -y \
    android-tools-adb \
    android-tools-fastboot \
    wget \
    unzip \
    && rm -rf /var/lib/apt/lists/*

WORKDIR /usr/app

ADD . .

RUN pip install --upgrade pip -i https://pypi.tuna.tsinghua.edu.cn/simple &&  \
    pip install -r requirements.txt -i https://pypi.tuna.tsinghua.edu.cn/simple

EXPOSE 8888

CMD ["python", "-u", "main.py"]
