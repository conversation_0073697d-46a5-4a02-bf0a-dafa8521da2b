from src.api import API_V1
from src.infra.app import app
from src.infra.events import event_bus
from src.scheduler import keep_scheduler

_ = keep_scheduler
_ = API_V1

if __name__ == "__main__":
    # ====== on start ======
    # monitor price
    # 测试的时候用
    # app.add_on_start(price_monitor_svc.start_all_monitor)
    # app.add_on_start(volume_monitor_svc.start_all_monitor)

    app.add_on_start(event_bus.start)
    app.add_on_start(app.scheduler.start)

    # ====== on stop ======

    app.add_on_stop(event_bus.stop)
    app.add_on_stop(app.scheduler.stop)
    app.launch()
