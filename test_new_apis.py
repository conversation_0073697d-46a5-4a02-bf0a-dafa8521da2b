#!/usr/bin/env python3
"""
测试新增的步骤评分和优化接口
"""

import sys
import os
# Add project root to Python path
sys.path.insert(0, os.path.join(os.path.dirname(__file__)))

from src.domain.ui_task.mobile.service.step_score_service import step_score_service
from src.domain.ui_task.mobile.service.step_optimize_new_service import step_optimize_new_service

# 测试用例
test_steps = """
点击页面右侧【进入世界】区域
点击除左上角返回按钮外的任意位置
点击除左上角返回按钮外的任意位置
点击底部选项中的【让AI想】
每隔3秒，检查当前弹窗底部是否出现【构建世界】按钮
点击【构建世界】
每隔5秒，检查右上角是否出现【进入】按钮
点击页面右侧【进入】
如果出现【世界观不完整】弹窗
点击弹窗中【进入世界】按钮
"""

def test_step_score():
    """测试步骤评分功能"""
    print("=== 测试步骤评分功能 ===")
    result, error = step_score_service.score_steps(test_steps)
    
    if error:
        print(f"❌ 评分失败: {error}")
        return False
    
    print("✅ 评分成功:")
    for i, score_result in enumerate(result, 1):
        print(f"步骤 {i}:")
        print(f"  内容: {score_result.get('step_sentence', '')}")
        print(f"  评分: {score_result.get('score', 0)}")
        print(f"  缺失要素: {score_result.get('missing_elements', [])}")
        print(f"  优化建议: {score_result.get('optimize_advice', '')}")
        print()
    
    return True

def test_step_optimize():
    """测试步骤优化功能"""
    print("=== 测试步骤优化功能 ===")
    result, error = step_optimize_new_service.optimize_steps(test_steps)
    
    if error:
        print(f"❌ 优化失败: {error}")
        return False
    
    print("✅ 优化成功:")
    for i, optimize_result in enumerate(result, 1):
        print(f"步骤 {i}:")
        print(f"  原始内容: {optimize_result.get('original_sentence', '')}")
        print(f"  优化内容: {optimize_result.get('optimized_sentence', '')}")
        print(f"  是否优化: {optimize_result.get('is_optimized', 'false')}")
        print()
    
    return True

if __name__ == "__main__":
    print("开始测试新增的步骤评分和优化功能...")
    
    # 测试评分功能
    score_success = test_step_score()
    
    print("\n" + "="*50 + "\n")
    
    # 测试优化功能
    optimize_success = test_step_optimize()
    
    print("\n" + "="*50)
    print("测试总结:")
    print(f"步骤评分: {'✅ 成功' if score_success else '❌ 失败'}")
    print(f"步骤优化: {'✅ 成功' if optimize_success else '❌ 失败'}")
