[tool.poetry]
name = "click-pilot"
version = "0.1.0"
description = "ClickPilot - 智能UI自动化测试Agent"
authors = ["liangyed<PERSON> <<EMAIL>>"]
readme = "README.md"
package-mode = false

[tool.poetry.dependencies]
python = "^3.11"
loguru = "^0.7.3"
fastapi = "^0.116.1"
uvicorn = "^0.35.0"
pydantic = "^2.11.7"
pydantic-settings = "^2.10.1"
sqlalchemy = "^2.0.41"
pymysql = "^1.1.1"
apscheduler = "^3.11.0"
requests = "^2.32.4"
tenacity = "^9.1.2"
urllib3 = "^2.5.0"
certifi = "^2025.7.14"
charset-normalizer = "^3.4.2"
idna = "^3.10"
tushare = "^1.4.21"
lark-oapi = "^1.4.19"
tomli = "^2.2.1"
alembic = "^1.16.4"
# UI自动化测试相关依赖
adbutils = "^2.9.3"
openai = "^1.78.1"
langchain = "^0.3.26"
langchain-core = "^0.3.66"
langchain-openai = "^0.3.16"
langgraph = "^0.2.76"
opencv-python = "^*********"
pillow = "^10.2.0"
numpy = "^1.26.4"
tomlkit = "^0.13.3"
python-multipart = "^0.0.20"

[tool.poetry.group.dev.dependencies]
ipykernel = "^6.30.0"



[build-system]
requires = ["poetry-core>=2.0.0,<3.0.0"]
build-backend = "poetry.core.masonry.api"
